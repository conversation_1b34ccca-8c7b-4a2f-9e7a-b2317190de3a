# Application Settings
ENV=
APP_NAME=user-service
DEBUG=false
PORT=50052

# Database Settings
DB_HOST=localhost
DB_PORT=5432
DB_USER=user_service
DB_PASSWORD=userpass
DB_NAME=user_db

# Redis Settings
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# JWT Settings
JWT_SECRET_KEY=your-secret-key-at-least-32-chars-long
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Google OAuth Settings
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8000/auth/google/callback

# Proto
REPO_URL=
GIT_TOKEN=

API_GATEWAY_URL=

FRONTEND_URL=

BOOTSTRAP_SERVERS=

GOOGLE_APPLICATION_CREDENTIALS=
GOOGLE_PROJECT_ID=

DEFAULT_PROFILE_PICTURE=