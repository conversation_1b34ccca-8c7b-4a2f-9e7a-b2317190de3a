"""
Test module for token expiry functionality.
Tests that access tokens expire in 1 day and refresh tokens expire in 7 days.
"""

import pytest
from jose import jwt
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from app.core.config import settings
from app.core.security import create_access_token, create_refresh_token


class TestTokenExpiry:
    """Test class for token expiry functionality."""

    def test_access_token_expiry_configuration(self):
        """Test that ACCESS_TOKEN_EXPIRE_MINUTES is set to 1440 (1 day)."""
        assert settings.ACCESS_TOKEN_EXPIRE_MINUTES == 1440, (
            f"Expected ACCESS_TOKEN_EXPIRE_MINUTES to be 1440, "
            f"but got {settings.ACCESS_TOKEN_EXPIRE_MINUTES}"
        )

    def test_refresh_token_expiry_configuration(self):
        """Test that REFRESH_TOKEN_EXPIRE_DAYS is set to 7 days."""
        assert settings.REFRESH_TOKEN_EXPIRE_DAYS == 7, (
            f"Expected REFRESH_TOKEN_EXPIRE_DAYS to be 7, "
            f"but got {settings.REFRESH_TOKEN_EXPIRE_DAYS}"
        )

    def test_token_expiry_consistency(self):
        """Test that JWT expiry and Redis TTL are consistent."""
        # Access token: 1440 minutes = 86400 seconds
        jwt_expiry_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        redis_ttl_seconds = jwt_expiry_minutes * 60

        assert redis_ttl_seconds == 86400, (
            f"Access token Redis TTL should be 86400 seconds, "
            f"but calculated {redis_ttl_seconds} seconds"
        )

        # Refresh token: 7 days = 604800 seconds
        jwt_expiry_days = settings.REFRESH_TOKEN_EXPIRE_DAYS
        redis_ttl_seconds = jwt_expiry_days * 24 * 60 * 60

        assert redis_ttl_seconds == 604800, (
            f"Refresh token Redis TTL should be 604800 seconds, "
            f"but calculated {redis_ttl_seconds} seconds"
        )

    def test_access_token_contains_correct_expiry(self):
        """Test that access token contains correct expiry time."""
        test_data = {"user_id": "test-user", "email": "<EMAIL>"}

        # Record time before creating token
        before_creation = datetime.utcnow()

        # Create token
        token = create_access_token(test_data)

        # Record time after creating token
        after_creation = datetime.utcnow()

        # Decode token to check expiry
        decoded = jwt.decode(
            token,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )

        # Check that expiry is within expected range
        exp_timestamp = decoded["exp"]
        exp_datetime = datetime.fromtimestamp(exp_timestamp)

        # Expected expiry should be 1440 minutes (1 day) from creation time
        min_expected_expiry = before_creation + timedelta(minutes=1440)
        max_expected_expiry = after_creation + timedelta(minutes=1440)

        assert min_expected_expiry <= exp_datetime <= max_expected_expiry, (
            f"Access token expiry {exp_datetime} is not within expected range "
            f"[{min_expected_expiry}, {max_expected_expiry}]"
        )

    def test_refresh_token_contains_correct_expiry(self):
        """Test that refresh token contains correct expiry time."""
        test_data = {"user_id": "test-user", "email": "<EMAIL>"}

        # Record time before creating token
        before_creation = datetime.utcnow()

        # Create token
        token = create_refresh_token(test_data)

        # Record time after creating token
        after_creation = datetime.utcnow()

        # Decode token to check expiry
        decoded = jwt.decode(
            token,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )

        # Check that expiry is within expected range
        exp_timestamp = decoded["exp"]
        exp_datetime = datetime.fromtimestamp(exp_timestamp)

        # Expected expiry should be 7 days from creation time
        min_expected_expiry = before_creation + timedelta(days=7)
        max_expected_expiry = after_creation + timedelta(days=7)

        assert min_expected_expiry <= exp_datetime <= max_expected_expiry, (
            f"Refresh token expiry {exp_datetime} is not within expected range "
            f"[{min_expected_expiry}, {max_expected_expiry}]"
        )

    @patch('app.services.auth_haders.RedisService')
    @patch('app.services.auth_haders.KafkaProducer')
    @patch('app.services.auth_haders.EncryptionManager')
    def test_auth_service_access_token_redis_ttl(self, mock_encryption, mock_kafka, mock_redis_service):
        """Test that AuthService sets correct Redis TTL for access tokens."""
        from app.services.auth_haders import AuthService

        # Mock Redis service
        mock_redis_instance = Mock()
        mock_redis_service.return_value = mock_redis_instance

        auth_service = AuthService()

        # Test data
        test_data = {
            "id": "test-user-id",
            "email": "<EMAIL>",
            "role": "user"
        }

        # Call the method
        result = auth_service._generate_access_token(test_data)

        # Verify Redis TTL was set correctly (1440 minutes * 60 seconds = 86400 seconds)
        expected_ttl = 1440 * 60  # 86400 seconds = 1 day
        mock_redis_instance.set_ttl_hset.assert_called_once()

        # Get the actual TTL value from the call
        call_args = mock_redis_instance.set_ttl_hset.call_args
        actual_ttl = call_args[0][1]  # Second argument is the TTL

        assert actual_ttl == expected_ttl, (
            f"Expected Redis TTL to be {expected_ttl} seconds, "
            f"but got {actual_ttl} seconds"
        )
