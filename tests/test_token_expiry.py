"""
Test module for token expiry functionality.
Tests that both access tokens and refresh tokens expire in 1 week (7 days).
Updated to test the new fixed 1-week expiration for all tokens.
"""

import pytest
from jose import jwt
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from app.core.config import settings
from app.core.security import create_access_token, create_refresh_token


class TestTokenExpiry:
    """Test class for token expiry functionality."""

    def test_access_token_expiry_configuration(self):
        """Test that ACCESS_TOKEN_EXPIRE_MINUTES configuration still exists (for backward compatibility)."""
        # Note: The AuthService now uses fixed 7-day expiration regardless of this setting
        assert hasattr(settings, 'ACCESS_TOKEN_EXPIRE_MINUTES'), (
            "ACCESS_TOKEN_EXPIRE_MINUTES setting should still exist for backward compatibility"
        )

    def test_refresh_token_expiry_configuration(self):
        """Test that REFRESH_TOKEN_EXPIRE_DAYS configuration still exists (for backward compatibility)."""
        # Note: The AuthService now uses fixed 7-day expiration regardless of this setting
        assert hasattr(settings, 'REFRESH_TOKEN_EXPIRE_DAYS'), (
            "REFRESH_TOKEN_EXPIRE_DAYS setting should still exist for backward compatibility"
        )

    def test_token_expiry_consistency(self):
        """Test that JWT expiry and Redis TTL are consistent with new 1-week expiration."""
        # Both access and refresh tokens now use 7 days = 604800 seconds
        expected_ttl_seconds = 7 * 24 * 60 * 60  # 604800 seconds = 7 days

        assert expected_ttl_seconds == 604800, (
            f"Expected TTL should be 604800 seconds (7 days), "
            f"but calculated {expected_ttl_seconds} seconds"
        )

        # Verify the calculation is correct
        assert 7 * 24 * 60 * 60 == 604800, (
            "7 days should equal 604800 seconds"
        )

    def test_access_token_contains_correct_expiry(self):
        """Test that access token contains correct expiry time (now 7 days)."""
        test_data = {"user_id": "test-user", "email": "<EMAIL>"}

        # Record time before creating token
        before_creation = datetime.now()

        # Create token
        token = create_access_token(test_data)

        # Record time after creating token
        after_creation = datetime.now()

        # Decode token to check expiry
        decoded = jwt.decode(
            token,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )

        # Check that expiry is within expected range
        exp_timestamp = decoded["exp"]
        exp_datetime = datetime.fromtimestamp(exp_timestamp)

        # Expected expiry should be based on config setting (with tolerance for execution time)
        min_expected_expiry = before_creation + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES) - timedelta(seconds=1)
        max_expected_expiry = after_creation + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES) + timedelta(seconds=1)

        assert min_expected_expiry <= exp_datetime <= max_expected_expiry, (
            f"Access token expiry {exp_datetime} is not within expected range "
            f"[{min_expected_expiry}, {max_expected_expiry}]"
        )

    def test_refresh_token_contains_correct_expiry(self):
        """Test that refresh token contains correct expiry time (7 days)."""
        test_data = {"user_id": "test-user", "email": "<EMAIL>"}

        # Record time before creating token
        before_creation = datetime.now()

        # Create token
        token = create_refresh_token(test_data)

        # Record time after creating token
        after_creation = datetime.now()

        # Decode token to check expiry
        decoded = jwt.decode(
            token,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )

        # Check that expiry is within expected range
        exp_timestamp = decoded["exp"]
        exp_datetime = datetime.fromtimestamp(exp_timestamp)

        # Expected expiry should be 7 days from creation time (using config setting with tolerance)
        min_expected_expiry = before_creation + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS) - timedelta(seconds=1)
        max_expected_expiry = after_creation + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS) + timedelta(seconds=1)

        assert min_expected_expiry <= exp_datetime <= max_expected_expiry, (
            f"Refresh token expiry {exp_datetime} is not within expected range "
            f"[{min_expected_expiry}, {max_expected_expiry}]"
        )

    @patch('app.services.auth_haders.RedisService')
    @patch('app.services.auth_haders.KafkaProducer')
    @patch('app.services.auth_haders.EncryptionManager')
    def test_auth_service_access_token_redis_ttl(self, mock_encryption, mock_kafka, mock_redis_service):
        """Test that AuthService sets correct Redis TTL for access tokens (now 7 days)."""
        from app.services.auth_haders import AuthService

        # Mock Redis service
        mock_redis_instance = Mock()
        mock_redis_service.return_value = mock_redis_instance

        auth_service = AuthService()

        # Test data
        test_data = {
            "id": "test-user-id",
            "email": "<EMAIL>",
            "role": "user"
        }

        # Call the method
        auth_service._generate_access_token(test_data)

        # Verify Redis TTL was set correctly (7 days * 24 hours * 60 minutes * 60 seconds = 604800 seconds)
        expected_ttl = 7 * 24 * 60 * 60  # 604800 seconds = 7 days
        mock_redis_instance.set_ttl_hset.assert_called_once()

        # Get the actual TTL value from the call
        call_args = mock_redis_instance.set_ttl_hset.call_args
        actual_ttl = call_args[0][1]  # Second argument is the TTL

        assert actual_ttl == expected_ttl, (
            f"Expected Redis TTL to be {expected_ttl} seconds (7 days), "
            f"but got {actual_ttl} seconds"
        )

    @patch('app.services.auth_haders.RedisService')
    @patch('app.services.auth_haders.KafkaProducer')
    @patch('app.services.auth_haders.EncryptionManager')
    def test_auth_service_refresh_token_redis_ttl(self, mock_encryption, mock_kafka, mock_redis_service):
        """Test that AuthService sets correct Redis TTL for refresh tokens (7 days)."""
        from app.services.auth_haders import AuthService

        # Mock Redis service
        mock_redis_instance = Mock()
        mock_redis_service.return_value = mock_redis_instance

        auth_service = AuthService()

        # Test data
        test_data = {
            "id": "test-user-id",
            "email": "<EMAIL>",
            "role": "user"
        }

        # Call the method
        auth_service._generate_refresh_token(test_data)

        # Verify Redis TTL was set correctly (7 days * 24 hours * 60 minutes * 60 seconds = 604800 seconds)
        expected_ttl = 7 * 24 * 60 * 60  # 604800 seconds = 7 days
        mock_redis_instance.set_ttl_hset.assert_called_once()

        # Get the actual TTL value from the call
        call_args = mock_redis_instance.set_ttl_hset.call_args
        actual_ttl = call_args[0][1]  # Second argument is the TTL

        assert actual_ttl == expected_ttl, (
            f"Expected Redis TTL to be {expected_ttl} seconds (7 days), "
            f"but got {actual_ttl} seconds"
        )

    @patch('app.services.auth_haders.RedisService')
    @patch('app.services.auth_haders.KafkaProducer')
    @patch('app.services.auth_haders.EncryptionManager')
    def test_auth_service_token_expiration_fixed_seven_days(self, mock_encryption, mock_kafka, mock_redis_service):
        """Test that AuthService uses fixed 7-day expiration regardless of config settings."""
        from app.services.auth_haders import AuthService
        from jose import jwt

        # Mock Redis service
        mock_redis_instance = Mock()
        mock_redis_service.return_value = mock_redis_instance

        auth_service = AuthService()

        # Test data
        test_data = {
            "id": "test-user-id",
            "email": "<EMAIL>",
            "role": "user"
        }

        # Record time before creating tokens
        before_creation = datetime.now()

        # Test access token
        access_result = auth_service._generate_access_token(test_data)
        access_token = access_result["token"]

        # Test refresh token
        refresh_result = auth_service._generate_refresh_token(test_data)
        refresh_token = refresh_result["refresh_token"]

        # Record time after creating tokens
        after_creation = datetime.now()

        # Decode both tokens to check expiry
        access_decoded = jwt.decode(access_token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        refresh_decoded = jwt.decode(refresh_token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])

        # Check that both tokens expire in 7 days
        access_exp_datetime = datetime.fromtimestamp(access_decoded["exp"])
        refresh_exp_datetime = datetime.fromtimestamp(refresh_decoded["exp"])

        # Expected expiry should be 7 days from creation time (with some tolerance for execution time)
        min_expected_expiry = before_creation + timedelta(days=7) - timedelta(seconds=1)
        max_expected_expiry = after_creation + timedelta(days=7) + timedelta(seconds=1)

        assert min_expected_expiry <= access_exp_datetime <= max_expected_expiry, (
            f"Access token expiry {access_exp_datetime} is not within expected 7-day range "
            f"[{min_expected_expiry}, {max_expected_expiry}]"
        )

        assert min_expected_expiry <= refresh_exp_datetime <= max_expected_expiry, (
            f"Refresh token expiry {refresh_exp_datetime} is not within expected 7-day range "
            f"[{min_expected_expiry}, {max_expected_expiry}]"
        )

        # Verify both tokens expire at approximately the same time (within 1 second)
        time_diff = abs((access_exp_datetime - refresh_exp_datetime).total_seconds())
        assert time_diff <= 1, (
            f"Access and refresh tokens should expire at approximately the same time, "
            f"but they differ by {time_diff} seconds"
        )

    @patch('app.services.auth_haders.RedisService')
    @patch('app.services.auth_haders.KafkaProducer')
    @patch('app.services.auth_haders.EncryptionManager')
    def test_login_response_includes_token_ages(self, mock_encryption, mock_kafka, mock_redis_service):
        """Test that login response includes accessTokenAge and refreshTokenAge."""
        from app.services.auth_haders import AuthService
        from app.grpc import user_pb2
        from unittest.mock import Mock
        import grpc

        # Mock Redis service
        mock_redis_instance = Mock()
        mock_redis_service.return_value = mock_redis_instance

        # Mock database and user
        auth_service = AuthService()

        # Create a mock user
        mock_user = Mock()
        mock_user.id = "test-user-id"
        mock_user.email = "<EMAIL>"
        mock_user.hashed_password = "$2b$12$test_hashed_password"
        mock_user.is_email_verified = True
        mock_user.is_first_login = False
        mock_user.full_name = "Test User"
        mock_user.created_at = datetime.now()
        mock_user.updated_at = datetime.now()
        mock_user.role = Mock()
        mock_user.role.value = "user"

        # Mock database session
        mock_db = Mock()
        mock_db.query.return_value.filter.return_value.first.return_value = mock_user

        # Patch the get_db method
        with patch.object(auth_service, 'get_db', return_value=mock_db):
            # Patch password verification
            with patch.object(auth_service, '_verify_password', return_value=True):
                # Create login request
                request = user_pb2.LoginRequest(
                    email="<EMAIL>",
                    password="test_password",
                    fcmToken="test_fcm_token"
                )

                # Mock context
                context = Mock()

                # Call login method
                response = auth_service.login(request, context)

                # Verify response structure
                assert response.success is True
                assert response.message == "Login successful"
                assert response.accessToken is not None
                assert response.refreshToken is not None
                assert hasattr(response, 'accessTokenAge')
                assert hasattr(response, 'refreshTokenAge')

                # Verify token ages are integers (seconds)
                assert isinstance(response.accessTokenAge, int)
                assert isinstance(response.refreshTokenAge, int)

                # Verify token ages are in seconds (7 days = 604800 seconds)
                expected_seconds = 7 * 24 * 60 * 60  # 604800 seconds

                assert response.accessTokenAge == expected_seconds, (
                    f"Access token age should be {expected_seconds} seconds, "
                    f"but got {response.accessTokenAge} seconds"
                )

                assert response.refreshTokenAge == expected_seconds, (
                    f"Refresh token age should be {expected_seconds} seconds, "
                    f"but got {response.refreshTokenAge} seconds"
                )

                # Verify both token ages are exactly the same
                assert response.accessTokenAge == response.refreshTokenAge, (
                    f"Access and refresh token ages should be the same, "
                    f"but got {response.accessTokenAge} and {response.refreshTokenAge}"
                )

    @patch('app.services.user_functions.RedisService')
    @patch('app.services.user_functions.KafkaProducer')
    @patch('app.services.user_functions.EncryptionManager')
    def test_access_token_refresh_returns_timestamp(self, mock_encryption, mock_kafka, mock_redis_service):
        """Test that access token refresh returns accessTokenAge as timestamp."""
        from app.services.user_functions import UserFunctions
        from app.grpc import user_pb2
        from unittest.mock import Mock
        from jose import jwt

        # Mock Redis service
        mock_redis_instance = Mock()
        mock_redis_service.return_value = mock_redis_instance

        # Create UserFunctions instance
        user_functions = UserFunctions()

        # Create a mock user
        mock_user = Mock()
        mock_user.id = "test-user-id"
        mock_user.email = "<EMAIL>"
        mock_user.role = "user"

        # Mock database session
        mock_db = Mock()
        mock_db.query.return_value.filter.return_value.first.return_value = mock_user

        # Create a valid refresh token
        current_time = int(datetime.now().timestamp())
        seven_days_later = current_time + (7 * 24 * 60 * 60)

        refresh_token_payload = {
            "exp": seven_days_later,
            "user_id": "test-user-id",
            "email": "<EMAIL>",
            "role": "user"
        }

        from app.core.config import settings
        refresh_token = jwt.encode(refresh_token_payload, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)

        # Mock Redis to return the same refresh token
        mock_redis_instance.get_data_from_redis.return_value = refresh_token

        # Patch the get_db method
        with patch.object(user_functions, 'get_db', return_value=mock_db):
            # Create access token request
            request = user_pb2.AccessTokenRequest(refreshToken=refresh_token)

            # Mock context
            context = Mock()

            # Call accessToken method
            response = user_functions.accessToken(request, context)

            # Verify response structure
            assert response.success is True
            assert response.accessToken is not None
            assert hasattr(response, 'accessTokenAge')

            # Verify accessTokenAge is a timestamp (not seconds)
            assert isinstance(response.accessTokenAge, int)

            # Verify accessTokenAge is a timestamp in the future (approximately 7 days from now)
            current_timestamp = int(datetime.now().timestamp())
            seven_days_timestamp = current_timestamp + (7 * 24 * 60 * 60)

            # Allow some tolerance for execution time
            min_expected_timestamp = seven_days_timestamp - 10
            max_expected_timestamp = seven_days_timestamp + 10

            assert min_expected_timestamp <= response.accessTokenAge <= max_expected_timestamp, (
                f"Access token age {response.accessTokenAge} is not within expected timestamp range "
                f"[{min_expected_timestamp}, {max_expected_timestamp}]"
            )
