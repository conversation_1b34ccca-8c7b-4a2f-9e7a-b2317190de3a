"""
Tests for the workflow schema converter.
"""

import json
import os
import sys
import pytest

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from app.services.workflow_schema_converter import convert_workflow_to_transition_schema


def test_approval_required_field():
    """
    Test that the approval_required field is correctly populated from the requires_approval field.
    """
    # Load the sample workflow
    with open(os.path.join("testing", "sample_workflow.json"), "r") as f:
        workflow_data = json.load(f)

    # Convert the workflow to transition schema
    transition_schema = convert_workflow_to_transition_schema(workflow_data)

    # Check that the transitions have the approval_required field
    for transition in transition_schema["transitions"]:
        node_id = transition["node_info"]["node_id"]
        transition_id = transition["id"].replace("transition-", "")

        # Find the corresponding node in the original workflow
        original_node = None

        # First, try to find the node by its ID matching the transition ID
        for node in workflow_data["nodes"]:
            if node["id"] == transition_id:
                original_node = node
                break

        # If we didn't find a node, try other methods
        if not original_node:
            for node in workflow_data["nodes"]:
                # Check if the node's server_id matches the node_id in the transition
                if (
                    "data" in node
                    and "definition" in node["data"]
                    and "mcp_info" in node["data"]["definition"]
                    and "server_id" in node["data"]["definition"]["mcp_info"]
                    and node["data"]["definition"]["mcp_info"]["server_id"] == node_id
                ):
                    original_node = node
                    break

                # Check if the node's definition name matches the node_id in the transition
                if (
                    "data" in node
                    and "definition" in node["data"]
                    and "name" in node["data"]["definition"]
                    and node["data"]["definition"]["name"] == node_id
                ):
                    original_node = node
                    break

        if original_node and "data" in original_node and "definition" in original_node["data"]:
            # Check if requires_approval is in the node definition
            requires_approval = original_node["data"]["definition"].get("requires_approval", False)

            # Check that the approval_required field matches
            assert transition.get("approval_required", False) == requires_approval, (
                f"Transition {transition['id']} has approval_required={transition.get('approval_required', False)}, "
                f"but node {original_node['id']} has requires_approval={requires_approval}"
            )


def test_start_node_ignored():
    """
    Test that the start node is ignored and nodes connected to it are marked as initial.
    """
    # Load the sample workflow
    with open(os.path.join("testing", "sample_workflow.json"), "r") as f:
        workflow_data = json.load(f)

    # Find the start node
    start_node_id = None
    for node in workflow_data["nodes"]:
        if (
            "data" in node
            and "definition" in node["data"]
            and "originalType" in node["data"]
            and node["data"]["originalType"] == "StartNode"
        ):
            start_node_id = node["id"]
            break

    assert start_node_id is not None, "Start node not found in the workflow"

    # Find nodes connected to the start node
    nodes_connected_to_start = []
    for edge in workflow_data["edges"]:
        if edge["source"] == start_node_id:
            nodes_connected_to_start.append(edge["target"])

    assert len(nodes_connected_to_start) > 0, "No nodes connected to the start node"

    # Convert the workflow to transition schema
    transition_schema = convert_workflow_to_transition_schema(workflow_data)

    # Check that the start node is not in the transitions
    for transition in transition_schema["transitions"]:
        assert transition["id"] != f"transition-{start_node_id}", "Start node should be ignored"

    # Check that nodes connected to the start node are marked as initial
    initial_transitions = []
    for transition in transition_schema["transitions"]:
        if transition["transition_type"] == "initial":
            # Extract the node ID from the transition ID
            transition_node_id = transition["id"].replace("transition-", "")
            initial_transitions.append(transition_node_id)

    # Check that at least one node connected to the start node is marked as initial
    assert any(
        node_id in initial_transitions for node_id in nodes_connected_to_start
    ), "No nodes connected to the start node are marked as initial"

    # Check that the start node is not in the nodes array
    for node in transition_schema["nodes"]:
        assert node["id"] != start_node_id, "Start node should not be in the nodes array"


def test_start_node_handles_set_to_null():
    """
    Test that handles attached to the start node are set to null.
    """
    # Load the sample workflow
    with open(os.path.join("testing", "sample_workflow.json"), "r") as f:
        workflow_data = json.load(f)

    # Find the start node
    start_node_id = None
    for node in workflow_data["nodes"]:
        if (
            "data" in node
            and "definition" in node["data"]
            and "originalType" in node["data"]
            and node["data"]["originalType"] == "StartNode"
        ):
            start_node_id = node["id"]
            break

    assert start_node_id is not None, "Start node not found in the workflow"

    # Find edges from the start node
    start_node_edges = []
    for edge in workflow_data["edges"]:
        if edge["source"] == start_node_id:
            start_node_edges.append(edge)

    assert len(start_node_edges) > 0, "No edges from the start node"

    # Convert the workflow to transition schema
    transition_schema = convert_workflow_to_transition_schema(workflow_data)

    # Check that field values connected to the start node are set to null
    for transition in transition_schema["transitions"]:
        transition_id = transition["id"].replace("transition-", "")

        # Check if this transition is connected to the start node
        for edge in start_node_edges:
            if edge["target"] == transition_id:
                target_handle = edge.get("targetHandle")

                # Check that the field value is null
                if (
                    target_handle
                    and "node_info" in transition
                    and "tools_to_use" in transition["node_info"]
                ):
                    for tool in transition["node_info"]["tools_to_use"]:
                        if "tool_params" in tool and "items" in tool["tool_params"]:
                            for item in tool["tool_params"]["items"]:
                                if item["field_name"] == target_handle:
                                    assert (
                                        item["field_value"] is None
                                    ), f"Field value for {target_handle} should be null, but got {item['field_value']}"


if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
