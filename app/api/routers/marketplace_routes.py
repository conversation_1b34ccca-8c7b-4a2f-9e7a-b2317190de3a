from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query, Depends
from app.services.agent_service import AgentServiceClient
from app.services.workflow_service import WorkflowServiceClient
from app.services.mcp_service import MCPServiceClient
from app.services.user_service import UserServiceClient
from app.core.auth_guard import role_required
from app.schemas.marketplace import (
    AgentWithMCPsInDB,
    MarketplaceAgentResponse,
    MarketplaceWorkflowResponse,
    MarketplaceMCPResponse,
    MarketplaceItemSortEnum,
    MarketplaceItemTypeEnum,
    MarketplaceItemResponse,
    PaginatedMarketplaceAgentResponse,
    PaginatedMarketplaceWorkflowResponse,
    PaginatedMarketplaceMCPResponse,
    PaginationMetadata,
    CombinedMarketplaceResponse,
    MarketplaceAgentDetailResponse,
    MarketplaceWorkflowDetailResponse,
    MarketplaceMCPDetailResponse,
    MarketplaceWorkflowDetail,
    MarketplaceMCPDetail,
    RateMarketplaceItemRequest,
    RateMarketplaceItemResponse,
    UseMarketplaceItemRequest,
    UseMarketplaceItemResponse,
)
from app.schemas.agent import CategoryEnum
from app.utils.parse_error import parse_error
from google.protobuf.json_format import MessageToDict
import json
from app.api.routers.workflow_routes import prepare_workflow_dict


async def validate_user(user_id: str) -> bool:
    """
    Validate that a user exists.

    Args:
        user_id: The ID of the user to validate

    Returns:
        True if the user exists, False otherwise

    Raises:
        HTTPException: If the user does not exist
    """
    validate_response = await user_service.validate_user(user_id)
    if not validate_response["success"]:
        raise HTTPException(status_code=400, detail=validate_response["message"])
    return True


async def enrich_items_with_owner_names(items: list) -> list:
    """
    Enrich a list of marketplace items with owner names.

    Args:
        items: List of marketplace items (agents, workflows, MCPs)

    Returns:
        The same list with owner_name field populated
    """
    # Collect all unique owner_ids
    owner_ids = []
    for item in items:
        if hasattr(item, "owner_id") and item.owner_id:
            owner_ids.append(item.owner_id)

    if not owner_ids:
        return items

    # Fetch user details for all owner_ids in a single batch operation
    try:
        user_details = await user_service.get_users_by_ids(owner_ids)

        # Add owner_name to each item
        for item in items:
            if hasattr(item, "owner_id") and item.owner_id and item.owner_id in user_details:
                item.owner_name = user_details[item.owner_id]["full_name"]
    except Exception as e:
        print(f"[WARNING] Error enriching items with owner names: {str(e)}")
        # Continue without owner names if there's an error

    return items


marketplace_router = APIRouter(prefix="/marketplace", tags=["marketplace"])
agent_service = AgentServiceClient()
workflow_service = WorkflowServiceClient()
mcp_service = MCPServiceClient()
user_service = UserServiceClient()


@marketplace_router.get("/agents", response_model=PaginatedMarketplaceAgentResponse)
async def get_marketplace_agents(
    page: int = Query(1, ge=1, description="Page number for pagination (starts at 1)"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page (1-100)"),
    search: Optional[str] = Query(None, description="Search term to filter by name or description"),
    department: Optional[str] = Query(None, description="Filter by agent department"),
    category: Optional[str] = Query(None, description="Filter by agent category"),
    tags: Optional[List[str]] = Query(
        None,
        description="Filter by tags list (e.g., ['sales', 'support'])",
    ),
    sort_by: Optional[MarketplaceItemSortEnum] = Query(
        MarketplaceItemSortEnum.NEWEST,
        description="Sort results by specified criteria",
    ),
):
    """
    Retrieve a paginated list of marketplace agents.

    This endpoint returns a list of public agents available in the marketplace,
    with various filtering and sorting options.

    ## Filters
    - **page**: Page number for pagination (starts at 1)
    - **page_size**: Number of agents per page (1-100)
    - **search**: Search term to filter agents by name or description
    - **department**: Filter by agent department
    - **category**: Filter by agent category
    - **tags**: Filter by tags array (e.g., ['sales', 'support'])
    - **sort_by**: Sort results by specified criteria (newest, oldest, most_popular, highest_rated)

    ## Tag Format
    Tags should be provided as an array of strings, for example:
    `["sales", "support", "marketing"]`

    ## Response
    Returns a paginated list of marketplace agents matching the filter criteria.

    ## Example
    ```
    GET /marketplace/agents?page=1&page_size=10&department=SALES&search=customer&tags=["sales","support"]&sort_by=NEWEST
    ```
    """
    try:

        if category and category not in [c.value for c in CategoryEnum]:
            raise HTTPException(status_code=400, detail=f"Invalid category: {category}")

        # Call the agent service to get marketplace agents
        response = await agent_service.get_marketplace_agents(
            page=page,
            page_size=page_size,
            search=search,
            department=department,
            category=category,
            tags=tags,
            sort_by=sort_by.value if sort_by else None,
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        agents = []
        for agent in response.agents:
            agent_dict = MessageToDict(agent, preserving_proto_field_name=True)

            # Fix field mix-up: status and created_at fields are swapped
            # Also, tags might be in the status field as a JSON string
            if "status" in agent_dict and isinstance(agent_dict["status"], str):
                # Check if status contains JSON data (which should be tags)
                try:
                    json_data = json.loads(agent_dict["status"])
                    if isinstance(json_data, dict):
                        # This is likely tags data in the status field
                        agent_dict["tags"] = json_data
                        # Set status to a default value if it's not present
                        if "created_at" in agent_dict and agent_dict["created_at"] == "active":
                            agent_dict["status"] = "active"
                            # Fix created_at field
                            if "updated_at" in agent_dict:
                                agent_dict["created_at"] = agent_dict["updated_at"]
                        else:
                            agent_dict["status"] = "active"
                except json.JSONDecodeError:
                    # If it's not JSON, it's probably a legitimate status value
                    pass

            # Parse JSON string tags to array if it's a string
            if isinstance(agent_dict.get("tags"), str) and agent_dict.get("tags"):
                try:
                    parsed_tags = json.loads(agent_dict["tags"])
                    if isinstance(parsed_tags, dict):
                        # Convert dict to array of strings
                        agent_dict["tags"] = [f"{k}:{v}" for k, v in parsed_tags.items()]
                    elif isinstance(parsed_tags, list):
                        agent_dict["tags"] = parsed_tags
                    else:
                        agent_dict["tags"] = []
                except json.JSONDecodeError:
                    agent_dict["tags"] = []

            # Fix visibility and updated_at if they contain datetime strings
            if "visibility" in agent_dict and agent_dict["visibility"].startswith("202"):
                # This is likely a datetime that should be in updated_at
                if "updated_at" not in agent_dict or not agent_dict["updated_at"]:
                    agent_dict["updated_at"] = agent_dict["visibility"]
                agent_dict["visibility"] = "PUBLIC"  # Default for marketplace items

            # Ensure created_at and updated_at are proper datetime strings
            for date_field in ["created_at", "updated_at"]:
                if date_field in agent_dict and not isinstance(agent_dict[date_field], datetime):
                    if (
                        isinstance(agent_dict[date_field], str)
                        and agent_dict[date_field] == "active"
                    ):
                        # This is a status value in a date field
                        agent_dict["status"] = agent_dict[date_field]
                        # Remove the incorrect date field to let it default
                        agent_dict.pop(date_field, None)
            try:
                agents.append(MarketplaceAgentResponse(**agent_dict))
            except Exception as e:
                print(f"[WARNING] Error parsing agent data: {str(e)}")
                print(f"[DEBUG] Problematic agent data: {agent_dict}")
                # Skip this agent and continue with others

        metadata = PaginationMetadata(
            total=response.total,
            page=response.page,
            page_size=response.page_size,
            total_pages=response.total_pages,
            has_next=response.has_next,
            has_prev=response.has_prev,
            next_page=response.next_page if response.has_next else None,
            prev_page=response.prev_page if response.has_prev else None,
        )

        # Enrich agents with owner names
        agents = await enrich_items_with_owner_names(agents)

        return PaginatedMarketplaceAgentResponse(data=agents, metadata=metadata)

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in get_marketplace_agents: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@marketplace_router.get("/workflows", response_model=PaginatedMarketplaceWorkflowResponse)
async def get_marketplace_workflows(
    page: int = Query(1, ge=1, description="Page number for pagination (starts at 1)"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page (1-100)"),
    search: Optional[str] = Query(None, description="Search term to filter by name or description"),
    category: Optional[str] = Query(None, description="Filter by workflow category"),
    tags: Optional[List[str]] = Query(
        None,
        description="Filter by tags list (e.g., ['sales', 'support'])",
    ),
    sort_by: Optional[MarketplaceItemSortEnum] = Query(
        MarketplaceItemSortEnum.NEWEST,
        description="Sort results by specified criteria",
    ),
):
    """
    Retrieve a paginated list of marketplace workflows.

    This endpoint returns a list of public workflows available in the marketplace,
    with various filtering and sorting options.

    ## Filters
    - **page**: Page number for pagination (starts at 1)
    - **page_size**: Number of workflows per page (1-100)
    - **search**: Search term to filter workflows by name or description
    - **category**: Filter by workflow category
    - **tags**: Filter by tags array (e.g., ['sales', 'support'])
    - **sort_by**: Sort results by specified criteria (newest, oldest, most_popular, highest_rated)

    ## Tag Format
    Tags should be provided as an array of strings, for example:
    `["sales", "support", "marketing"]`

    ## Response
    Returns a paginated list of marketplace workflows matching the filter criteria.

    ## Example
    ```
    GET /marketplace/workflows?page=1&page_size=10&category=AUTOMATION&search=customer&tags=["automation","workflow"]&sort_by=NEWEST
    ```
    """
    try:
        # Validate enum values if provided
        if category and category not in [c.value for c in CategoryEnum]:
            raise HTTPException(status_code=400, detail=f"Invalid category: {category}")

        # Ensure tags is properly formatted as an array
        if tags:
            if isinstance(tags, str):
                try:
                    parsed_tags = json.loads(tags)
                    if not isinstance(parsed_tags, list):
                        parsed_tags = [parsed_tags] if parsed_tags else []
                except json.JSONDecodeError:
                    parsed_tags = [tags]
            else:
                parsed_tags = tags
        else:
            parsed_tags = None

        # Call the workflow service to get marketplace workflows
        response = await workflow_service.get_marketplace_workflows(
            page=page,
            page_size=page_size,
            search=search,
            category=category,
            tags=parsed_tags,
            sort_by=sort_by.value if sort_by else None,
        )

        print("[DEBUG] Workflow response: ", response)

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        workflows = []
        for workflow in response.workflows:
            workflow_dict = prepare_workflow_dict(
                MessageToDict(workflow, preserving_proto_field_name=True)
            )

            # Parse JSON string tags to array if it's a string
            if isinstance(workflow_dict.get("tags"), str) and workflow_dict.get("tags"):
                try:
                    parsed_tags = json.loads(workflow_dict["tags"])
                    if isinstance(parsed_tags, dict):
                        # Convert dict to array of strings
                        workflow_dict["tags"] = [f"{k}:{v}" for k, v in parsed_tags.items()]
                    elif isinstance(parsed_tags, list):
                        workflow_dict["tags"] = parsed_tags
                    else:
                        workflow_dict["tags"] = []
                except json.JSONDecodeError:
                    workflow_dict["tags"] = []

            workflows.append(MarketplaceWorkflowResponse(**workflow_dict))

        metadata = PaginationMetadata(
            total=response.total,
            page=response.page,
            page_size=response.page_size,
            total_pages=response.total_pages,
            has_next=response.has_next,
            has_prev=response.has_prev,
            next_page=response.next_page if response.has_next else None,
            prev_page=response.prev_page if response.has_prev else None,
        )

        # Enrich workflows with owner names
        workflows = await enrich_items_with_owner_names(workflows)

        return PaginatedMarketplaceWorkflowResponse(data=workflows, metadata=metadata)

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in get_marketplace_workflows: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@marketplace_router.get("/mcps", response_model=PaginatedMarketplaceMCPResponse)
async def get_marketplace_mcps(
    page: int = Query(1, ge=1, description="Page number for pagination (starts at 1)"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page (1-100)"),
    search: Optional[str] = Query(None, description="Search term to filter by name or description"),
    category: Optional[str] = Query(None, description="Filter by MCP category"),
    tags: Optional[List[str]] = Query(
        None,
        description="Filter by tags list (e.g., ['sales', 'support'])",
    ),
    sort_by: Optional[MarketplaceItemSortEnum] = Query(
        MarketplaceItemSortEnum.NEWEST,
        description="Sort results by specified criteria",
    ),
):
    """
    Retrieve a paginated list of marketplace MCP servers.

    This endpoint returns a list of public MCP servers available in the marketplace,
    with various filtering and sorting options.

    ## Filters
    - **page**: Page number for pagination (starts at 1)
    - **page_size**: Number of MCP servers per page (1-100)
    - **search**: Search term to filter MCPs by name or description
    - **category**: Filter by MCP category
    - **tags**: Filter by tags array (e.g., ['sales', 'support'])
    - **sort_by**: Sort results by specified criteria (newest, oldest, most_popular, highest_rated)

    ## Tag Format
    Tags should be provided as an array of strings, for example:
    `["sales", "support", "marketing"]`

    ## Response
    Returns a paginated list of marketplace MCP servers matching the filter criteria.

    ## Example
    ```
    GET /marketplace/mcps?page=1&page_size=10&category=API&search=connector&tags=["api","connector"]&sort_by=NEWEST
    ```
    """
    try:
        # Validate enum values if provided
        if category and category not in [c.value for c in CategoryEnum]:
            raise HTTPException(status_code=400, detail=f"Invalid category: {category}")

        # Ensure tags is properly formatted as an array
        if tags:
            if isinstance(tags, str):
                try:
                    parsed_tags = json.loads(tags)
                    if not isinstance(parsed_tags, list):
                        parsed_tags = [parsed_tags] if parsed_tags else []
                except json.JSONDecodeError:
                    parsed_tags = [tags]
            else:
                parsed_tags = tags
        else:
            parsed_tags = None

        # Call the MCP service to get marketplace MCPs
        response = await mcp_service.get_marketplace_mcps(
            page=page,
            page_size=page_size,
            search=search,
            category=category,
            tags=parsed_tags,
            sort_by=sort_by.value if sort_by else None,
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        mcps = []
        for mcp in response.mcps:
            mcp_dict = MessageToDict(mcp, preserving_proto_field_name=True)

            # Parse JSON string tags to array if it's a string
            if isinstance(mcp_dict.get("tags"), str) and mcp_dict.get("tags"):
                try:
                    parsed_tags = json.loads(mcp_dict["tags"])
                    if isinstance(parsed_tags, dict):
                        # Convert dict to array of strings
                        mcp_dict["tags"] = [f"{k}:{v}" for k, v in parsed_tags.items()]
                    elif isinstance(parsed_tags, list):
                        mcp_dict["tags"] = parsed_tags
                    else:
                        mcp_dict["tags"] = []
                except json.JSONDecodeError:
                    mcp_dict["tags"] = []

            mcp_dict["category"] = mcp_dict.get("department")

            mcps.append(MarketplaceMCPResponse(**mcp_dict))

        metadata = PaginationMetadata(
            total=response.total,
            page=response.page,
            page_size=response.page_size,
            total_pages=response.total_pages,
            has_next=response.has_next,
            has_prev=response.has_prev,
            next_page=response.next_page if response.has_next else None,
            prev_page=response.prev_page if response.has_prev else None,
        )

        # Enrich MCPs with owner names
        mcps = await enrich_items_with_owner_names(mcps)

        return PaginatedMarketplaceMCPResponse(data=mcps, metadata=metadata)

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in get_marketplace_mcps: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@marketplace_router.get("/all", response_model=CombinedMarketplaceResponse)
async def get_all_marketplace_items(
    page: int = Query(1, ge=1, description="Page number for pagination (starts at 1)"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page (1-100)"),
    search: Optional[str] = Query(None, description="Search term to filter by name or description"),
    category_type: Optional[MarketplaceItemTypeEnum] = Query(
        None, description="Filter by item type (AGENT, WORKFLOW, MCP)"
    ),
    category: Optional[str] = Query(None, description="Filter by MCP category"),
    tags: Optional[List[str]] = Query(
        None,
        description="Filter by tags list (e.g., ['sales', 'support'])",
    ),
    sort_by: Optional[MarketplaceItemSortEnum] = Query(
        MarketplaceItemSortEnum.NEWEST,
        description="Sort results by specified criteria",
    ),
):
    """
    Retrieve a combined paginated list of all marketplace items (agents, workflows, and MCPs).

    This endpoint returns a unified list of public marketplace items, with various filtering and sorting options.

    ## Filters
    - **page**: Page number for pagination (starts at 1)
    - **page_size**: Number of items per page (1-100)
    - **search**: Search term to filter items by name or description
    - **category_type**: Filter by item type (AGENT, WORKFLOW, MCP)
    - **tags**: Filter by tags array (e.g., ['sales', 'support'])
    - **sort_by**: Sort results by specified criteria (newest, oldest, most_popular, highest_rated)

    ## Tag Format
    Tags should be provided as an array of strings, for example:
    `["sales", "support", "marketing"]`

    ## Response
    Returns a combined paginated list of marketplace items matching the filter criteria.
    Each item includes a "type" field indicating whether it's an AGENT, WORKFLOW, or MCP.

    ## Example
    ```
    GET /marketplace/all?page=1&page_size=10&search=customer&tags=["sales","support"]&sort_by=NEWEST
    ```
    """
    try:
        # Ensure tags is properly formatted as an array
        if tags:
            if isinstance(tags, str):
                try:
                    parsed_tags = json.loads(tags)
                    if not isinstance(parsed_tags, list):
                        parsed_tags = [parsed_tags] if parsed_tags else []
                except json.JSONDecodeError:
                    parsed_tags = [tags]
            else:
                parsed_tags = tags
        else:
            parsed_tags = None

        # No need to validate category_type as it's now an enum parameter

        # Prepare the combined results list
        combined_items = []

        # Get agents if no category_type filter or if category_type is AGENT
        if not category_type or category_type == MarketplaceItemTypeEnum.AGENT:
            try:
                agent_response = await agent_service.get_marketplace_agents(
                    page=page,
                    page_size=page_size,
                    search=search,
                    department=category,
                    category=category,
                    tags=parsed_tags,
                    sort_by=sort_by.value if sort_by else None,
                )

                if agent_response.success:
                    for agent in agent_response.agents:
                        agent_dict = MessageToDict(agent, preserving_proto_field_name=True)

                        # Fix field mix-up: status and created_at fields are swapped
                        # Also, tags might be in the status field as a JSON string
                        if "status" in agent_dict and isinstance(agent_dict["status"], str):
                            # Check if status contains JSON data (which should be tags)
                            try:
                                json_data = json.loads(agent_dict["status"])
                                if isinstance(json_data, dict):
                                    # This is likely tags data in the status field
                                    agent_dict["tags"] = json_data
                                    # Set status to a default value if it's not present
                                    if (
                                        "created_at" in agent_dict
                                        and agent_dict["created_at"] == "active"
                                    ):
                                        agent_dict["status"] = "active"
                                        # Fix created_at field
                                        if "updated_at" in agent_dict:
                                            agent_dict["created_at"] = agent_dict["updated_at"]
                                    else:
                                        agent_dict["status"] = "active"
                            except json.JSONDecodeError:
                                # If it's not JSON, it's probably a legitimate status value
                                pass

                        # Parse JSON string tags to array if it's a string
                        if isinstance(agent_dict.get("tags"), str) and agent_dict.get("tags"):
                            try:
                                parsed_tags = json.loads(agent_dict["tags"])
                                if isinstance(parsed_tags, dict):
                                    # Convert dict to array of strings
                                    agent_dict["tags"] = [
                                        f"{k}:{v}" for k, v in parsed_tags.items()
                                    ]
                                elif isinstance(parsed_tags, list):
                                    agent_dict["tags"] = parsed_tags
                                else:
                                    agent_dict["tags"] = []
                            except json.JSONDecodeError:
                                agent_dict["tags"] = []

                        # Fix visibility and updated_at if they contain datetime strings
                        if "visibility" in agent_dict and agent_dict["visibility"].startswith(
                            "202"
                        ):
                            # This is likely a datetime that should be in updated_at
                            if "updated_at" not in agent_dict or not agent_dict["updated_at"]:
                                agent_dict["updated_at"] = agent_dict["visibility"]
                            agent_dict["visibility"] = "PUBLIC"  # Default for marketplace items

                        # Ensure created_at and updated_at are proper datetime strings
                        for date_field in ["created_at", "updated_at"]:
                            if date_field in agent_dict and not isinstance(
                                agent_dict[date_field], datetime
                            ):
                                if (
                                    isinstance(agent_dict[date_field], str)
                                    and agent_dict[date_field] == "active"
                                ):
                                    # This is a status value in a date field
                                    agent_dict["status"] = agent_dict[date_field]
                                    # Remove the incorrect date field to let it default
                                    agent_dict.pop(date_field, None)

                        # Add type field to identify this as an agent
                        agent_dict["item_type"] = MarketplaceItemTypeEnum.AGENT

                        try:
                            combined_items.append(MarketplaceItemResponse(**agent_dict))
                        except Exception as e:
                            print(f"[WARNING] Error parsing agent data in combined view: {str(e)}")
                            print(f"[DEBUG] Problematic agent data: {agent_dict}")
                            # Skip this agent and continue with others
            except Exception as e:
                print(f"[WARNING] Error fetching agents in get_all_marketplace_items: {str(e)}")
                # Continue execution to fetch other item types

        # Get workflows if no category_type filter or if category_type is WORKFLOW
        if not category_type or category_type == MarketplaceItemTypeEnum.WORKFLOW:
            try:
                workflow_response = await workflow_service.get_marketplace_workflows(
                    page=page,
                    page_size=page_size,
                    search=search,
                    category=category,
                    tags=parsed_tags,
                    sort_by=sort_by.value if sort_by else None,
                )

                if workflow_response.success:
                    for workflow in workflow_response.workflows:
                        workflow_dict = prepare_workflow_dict(
                            MessageToDict(workflow, preserving_proto_field_name=True)
                        )

                        # Parse JSON string tags to array if it's a string
                        if isinstance(workflow_dict.get("tags"), str) and workflow_dict.get("tags"):
                            try:
                                parsed_tags = json.loads(workflow_dict["tags"])
                                if isinstance(parsed_tags, dict):
                                    # Convert dict to array of strings
                                    workflow_dict["tags"] = [
                                        f"{k}:{v}" for k, v in parsed_tags.items()
                                    ]
                                elif isinstance(parsed_tags, list):
                                    workflow_dict["tags"] = parsed_tags
                                else:
                                    workflow_dict["tags"] = []
                            except json.JSONDecodeError:
                                workflow_dict["tags"] = []

                        # Add type field to identify this as a workflow
                        workflow_dict["item_type"] = MarketplaceItemTypeEnum.WORKFLOW

                        combined_items.append(MarketplaceItemResponse(**workflow_dict))
            except Exception as e:
                print(f"[WARNING] Error fetching workflows in get_all_marketplace_items: {str(e)}")
                # Continue execution to fetch other item types

        # Get MCPs if no category_type filter or if category_type is MCP
        if not category_type or category_type == MarketplaceItemTypeEnum.MCP:
            try:
                mcp_response = await mcp_service.get_marketplace_mcps(
                    page=page,
                    page_size=page_size,
                    search=search,
                    category=category,
                    tags=parsed_tags,
                    sort_by=sort_by.value if sort_by else None,
                )

                if mcp_response.success:
                    for mcp in mcp_response.mcps:
                        mcp_dict = MessageToDict(mcp, preserving_proto_field_name=True)

                        # Parse JSON string tags to array if it's a string
                        if isinstance(mcp_dict.get("tags"), str) and mcp_dict.get("tags"):
                            try:
                                parsed_tags = json.loads(mcp_dict["tags"])
                                if isinstance(parsed_tags, dict):
                                    # Convert dict to array of strings
                                    mcp_dict["tags"] = [f"{k}:{v}" for k, v in parsed_tags.items()]
                                elif isinstance(parsed_tags, list):
                                    mcp_dict["tags"] = parsed_tags
                                else:
                                    mcp_dict["tags"] = []
                            except json.JSONDecodeError:
                                mcp_dict["tags"] = []

                        # Add type field to identify this as an MCP
                        mcp_dict["item_type"] = MarketplaceItemTypeEnum.MCP
                        mcp_dict["category"] = mcp_dict.get("department")

                        combined_items.append(MarketplaceItemResponse(**mcp_dict))
            except Exception as e:
                print(f"[WARNING] Error fetching MCPs in get_all_marketplace_items: {str(e)}")
                # Continue execution to process the combined results

        # Sort the combined results based on the sort_by parameter
        try:
            if sort_by:
                if sort_by == MarketplaceItemSortEnum.NEWEST:
                    combined_items.sort(
                        key=lambda x: x.created_at if x.created_at else datetime.min, reverse=True
                    )
                elif sort_by == MarketplaceItemSortEnum.OLDEST:
                    combined_items.sort(
                        key=lambda x: x.created_at if x.created_at else datetime.max
                    )
                elif sort_by == MarketplaceItemSortEnum.MOST_POPULAR:
                    combined_items.sort(
                        key=lambda x: x.downloads if x.downloads else 0, reverse=True
                    )
                elif sort_by == MarketplaceItemSortEnum.HIGHEST_RATED:
                    combined_items.sort(key=lambda x: x.rating if x.rating else 0, reverse=True)
        except Exception as e:
            print(f"[WARNING] Error sorting items in get_all_marketplace_items: {str(e)}")
            # Continue with unsorted items

        try:
            # Calculate total items and pages
            total_items = len(combined_items)
            total_pages = (total_items + page_size - 1) // page_size if total_items > 0 else 1

            # Apply pagination
            start_idx = (page - 1) * page_size
            end_idx = min(start_idx + page_size, total_items)
            paginated_items = combined_items[start_idx:end_idx]

            # Create pagination metadata
            metadata = PaginationMetadata(
                total=total_items,
                page=page,
                page_size=page_size,
                total_pages=total_pages,
                has_next=page < total_pages,
                has_prev=page > 1,
                next_page=page + 1 if page < total_pages else None,
                prev_page=page - 1 if page > 1 else None,
            )
        except Exception as e:
            print(f"[WARNING] Error paginating items in get_all_marketplace_items: {str(e)}")
            # Return all items without pagination if pagination fails
            paginated_items = combined_items
            metadata = PaginationMetadata(
                total=len(combined_items),
                page=1,
                page_size=len(combined_items),
                total_pages=1,
                has_next=False,
                has_prev=False,
                next_page=None,
                prev_page=None,
            )

        try:
            # Enrich items with owner names
            paginated_items = await enrich_items_with_owner_names(paginated_items)

            return CombinedMarketplaceResponse(data=paginated_items, metadata=metadata)
        except Exception as e:
            print(f"[WARNING] Error creating response in get_all_marketplace_items: {str(e)}")
            # Return an empty response as a last resort
            return CombinedMarketplaceResponse(
                data=[],
                metadata=PaginationMetadata(
                    total=0,
                    page=1,
                    page_size=page_size,
                    total_pages=0,
                    has_next=False,
                    has_prev=False,
                    next_page=None,
                    prev_page=None,
                ),
            )

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in get_all_marketplace_items: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@marketplace_router.get("/agents/{agent_id}", response_model=MarketplaceAgentDetailResponse)
async def get_marketplace_agent_detail(
    agent_id: str,
    user_id: Optional[str] = Query(None, description="Optional user ID for personalized results"),
):
    """
    Retrieve detailed information about a specific marketplace agent.

    This endpoint returns comprehensive details about a public agent available in the marketplace,
    including its configuration, capabilities, and metadata.

    ## Path Parameters
    - **agent_id**: The unique identifier of the agent to retrieve

    ## Response
    Returns detailed information about the specified marketplace agent.

    ## Example
    ```
    GET /marketplace/agents/abc123
    ```
    """
    try:
        # Call the agent service to get the agent details
        response = await agent_service.get_marketplace_agent_detail(
            agent_id=agent_id, user_id=user_id
        )

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        agent_dict = MessageToDict(response.agent, preserving_proto_field_name=True)

        # Fix field mix-up: status and created_at fields are swapped
        # Also, tags might be in the status field as a JSON string
        if "status" in agent_dict and isinstance(agent_dict["status"], str):
            # Check if status contains JSON data (which should be tags)
            try:
                json_data = json.loads(agent_dict["status"])
                if isinstance(json_data, dict):
                    # This is likely tags data in the status field
                    agent_dict["tags"] = json_data
                    # Set status to a default value if it's not present
                    if "created_at" in agent_dict and agent_dict["created_at"] == "active":
                        agent_dict["status"] = "active"
                        # Fix created_at field
                        if "updated_at" in agent_dict:
                            agent_dict["created_at"] = agent_dict["updated_at"]
                    else:
                        agent_dict["status"] = "active"
            except json.JSONDecodeError:
                # If it's not JSON, it's probably a legitimate status value
                pass

        # Parse JSON string tags to array if it's a string
        if isinstance(agent_dict.get("tags"), str) and agent_dict.get("tags"):
            try:
                parsed_tags = json.loads(agent_dict["tags"])
                if isinstance(parsed_tags, dict):
                    # Convert dict to array of strings
                    agent_dict["tags"] = [f"{k}:{v}" for k, v in parsed_tags.items()]
                elif isinstance(parsed_tags, list):
                    agent_dict["tags"] = parsed_tags
                else:
                    agent_dict["tags"] = []
            except json.JSONDecodeError:
                agent_dict["tags"] = []

        # Fix visibility and updated_at if they contain datetime strings
        if "visibility" in agent_dict and agent_dict["visibility"].startswith("202"):
            # This is likely a datetime that should be in updated_at
            if "updated_at" not in agent_dict or not agent_dict["updated_at"]:
                agent_dict["updated_at"] = agent_dict["visibility"]
            agent_dict["visibility"] = "PUBLIC"  # Default for marketplace items

        # Ensure created_at and updated_at are proper datetime strings
        for date_field in ["created_at", "updated_at"]:
            if date_field in agent_dict and not isinstance(agent_dict[date_field], datetime):
                if isinstance(agent_dict[date_field], str) and agent_dict[date_field] == "active":
                    # This is a status value in a date field
                    agent_dict["status"] = agent_dict[date_field]
                    # Remove the incorrect date field to let it default
                    agent_dict.pop(date_field, None)

        # # Check if the agent is public (marketplace only shows public agents)
        # if agent_dict.get("visibility") != "public":
        #     raise HTTPException(status_code=404, detail="Agent not found in marketplace")
        mcp_ids = agent_dict.get("mcp_server_ids")
        mcps = []
        if mcp_ids:
            try:
                mcp_response = await mcp_service.getMCPsByIds(mcp_ids)
                print(f"[DEBUG] MCPs response: {mcp_response}")

                if hasattr(mcp_response, "mcps"):
                    for mcp in mcp_response.mcps:
                        mcp_dict = MessageToDict(mcp, preserving_proto_field_name=True)

                        mcps.append(mcp_dict)
            except Exception as e:
                print(f"[WARNING] Error fetching MCPs in get_agent_for_agent_platform: {str(e)}")
                raise HTTPException(status_code=404, detail=response.message)

        # Add MCPs to agent dict
        agent_dict["mcps"] = mcps

        workflow_ids = agent_dict.get("workflow_ids")
        workflows = []
        if workflow_ids:

            try:
                workflow_response = await workflow_service.get_workflows_by_ids(workflow_ids)
                print(f"[DEBUG] Workflows response: {workflow_response}")

                if hasattr(workflow_response, "workflows"):
                    for workflow in workflow_response.workflows:
                        workflow_dict = prepare_workflow_dict(
                            MessageToDict(workflow, preserving_proto_field_name=True)
                        )
                        workflows.append(workflow_dict)
            except Exception as e:
                print(
                    f"[WARNING] Error fetching workflows in get_agent_for_agent_platform: {str(e)}"
                )
                raise HTTPException(status_code=404, detail=response.message)

        agent_dict["workflows"] = workflows

        # Add owner name if owner_id exists
        if "owner_id" in agent_dict and agent_dict["owner_id"]:
            try:
                user_details = await user_service.get_users_by_ids([agent_dict["owner_id"]])
                if agent_dict["owner_id"] in user_details:
                    agent_dict["owner_name"] = user_details[agent_dict["owner_id"]]["full_name"]
            except Exception as e:
                print(f"[WARNING] Error fetching owner name for agent detail: {str(e)}")

        return MarketplaceAgentDetailResponse(
            success=True,
            message="Agent details retrieved successfully",
            agent=AgentWithMCPsInDB(**agent_dict),
        )

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in get_marketplace_agent_detail: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@marketplace_router.get(
    "/workflows/{workflow_id}", response_model=MarketplaceWorkflowDetailResponse
)
async def get_marketplace_workflow_detail(
    workflow_id: str,
    user_id: Optional[str] = Query(None, description="Optional user ID for personalized results"),
):
    """
    Retrieve detailed information about a specific marketplace workflow.

    This endpoint returns comprehensive details about a public workflow available in the marketplace,
    including its configuration, steps, and metadata.

    ## Path Parameters
    - **workflow_id**: The unique identifier of the workflow to retrieve

    ## Response
    Returns detailed information about the specified marketplace workflow.

    ## Example
    ```
    GET /marketplace/workflows/abc123
    ```
    """
    try:
        # Call the workflow service to get the workflow details
        response = await workflow_service.get_template(template_id=workflow_id, user_id=user_id)

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        workflow_dict = prepare_workflow_dict(
            MessageToDict(response.template, preserving_proto_field_name=True)
        )

        # Parse JSON string tags to array if it's a string
        if isinstance(workflow_dict.get("tags"), str) and workflow_dict.get("tags"):
            try:
                parsed_tags = json.loads(workflow_dict["tags"])
                if isinstance(parsed_tags, dict):
                    # Convert dict to array of strings
                    workflow_dict["tags"] = [f"{k}:{v}" for k, v in parsed_tags.items()]
                elif isinstance(parsed_tags, list):
                    workflow_dict["tags"] = parsed_tags
                else:
                    workflow_dict["tags"] = []
            except json.JSONDecodeError:
                workflow_dict["tags"] = []

        # # Check if the workflow is public (marketplace only shows public workflows)
        # if workflow_dict.get("visibility") != "PUBLIC":
        #     raise HTTPException(status_code=404, detail="Workflow not found in marketplace")

        # Add owner name if owner_id exists
        if "owner_id" in workflow_dict and workflow_dict["owner_id"]:
            try:
                user_details = await user_service.get_users_by_ids([workflow_dict["owner_id"]])
                if workflow_dict["owner_id"] in user_details:
                    workflow_dict["owner_name"] = user_details[workflow_dict["owner_id"]][
                        "full_name"
                    ]
            except Exception as e:
                print(f"[WARNING] Error fetching owner name for workflow detail: {str(e)}")

        return MarketplaceWorkflowDetailResponse(
            success=True,
            message="Workflow details retrieved successfully",
            workflow=MarketplaceWorkflowDetail(**workflow_dict),
        )

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in get_marketplace_workflow_detail: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@marketplace_router.get("/mcps/{mcp_id}", response_model=MarketplaceMCPDetailResponse)
async def get_marketplace_mcp_detail(
    mcp_id: str,
    user_id: Optional[str] = Query(None, description="Optional user ID for personalized results"),
):
    """
    Retrieve detailed information about a specific marketplace MCP server.

    This endpoint returns comprehensive details about a public MCP server available in the marketplace,
    including its configuration, capabilities, and metadata.

    ## Path Parameters
    - **mcp_id**: The unique identifier of the MCP server to retrieve

    ## Response
    Returns detailed information about the specified marketplace MCP server.

    ## Example
    ```
    GET /marketplace/mcps/abc123
    ```
    """
    try:
        # Call the MCP service to get the MCP details
        response = await mcp_service.getMCPByIdMarketplace(mcp_id=mcp_id, user_id=user_id)

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)

        # Parse JSON string tags to array if it's a string
        if isinstance(mcp_dict.get("tags"), str) and mcp_dict.get("tags"):
            try:
                parsed_tags = json.loads(mcp_dict["tags"])
                if isinstance(parsed_tags, dict):
                    # Convert dict to array of strings
                    mcp_dict["tags"] = [f"{k}:{v}" for k, v in parsed_tags.items()]
                elif isinstance(parsed_tags, list):
                    mcp_dict["tags"] = parsed_tags
                else:
                    mcp_dict["tags"] = []
            except json.JSONDecodeError:
                mcp_dict["tags"] = []

        # Check if the MCP is public (marketplace only shows public MCPs)
        if mcp_dict.get("visibility") != "public":
            raise HTTPException(status_code=404, detail="MCP not found in marketplace")

        # Add owner name if owner_id exists
        if "owner_id" in mcp_dict and mcp_dict["owner_id"]:
            try:
                user_details = await user_service.get_users_by_ids([mcp_dict["owner_id"]])
                if mcp_dict["owner_id"] in user_details:
                    mcp_dict["owner_name"] = user_details[mcp_dict["owner_id"]]["full_name"]
            except Exception as e:
                print(f"[WARNING] Error fetching owner name for MCP detail: {str(e)}")

        mcp_dict["category"] = mcp_dict.get("department")

        return MarketplaceMCPDetailResponse(
            success=True,
            message="MCP details retrieved successfully",
            mcp=MarketplaceMCPDetail(**mcp_dict),
        )

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in get_marketplace_mcp_detail: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@marketplace_router.post("/rate", response_model=RateMarketplaceItemResponse)
async def rate_marketplace_item(
    rating_data: RateMarketplaceItemRequest,
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Rate a marketplace item (agent, workflow, or MCP).

    This endpoint allows users to rate a marketplace item with a score from 1 to 5.

    ## Request Body
    - **item_id**: The ID of the item to rate
    - **item_type**: The type of item (AGENT, WORKFLOW, MCP)
    - **rating**: Rating value between 1.0 and 5.0

    ## Response
    Returns a success message and the updated rating information.

    ## Errors
    - 400: Invalid request (invalid item type, rating out of range)
    - 403: Permission denied (not authorized)
    - 404: Item not found
    - 500: Server error
    """
    try:
        # Validate that the user exists
        await validate_user(current_user["user_id"])

        user_id = current_user["user_id"]

        # user_id = "91a237fd-0225-4e02-9e9f-805eff073b07"
        item_id = rating_data.item_id
        rating = rating_data.rating
        item_type = rating_data.item_type

        # Call the appropriate service based on item type
        if item_type == MarketplaceItemTypeEnum.AGENT:
            response = await agent_service.rate_agent(
                agent_id=item_id, user_id=user_id, rating=rating
            )

            if not response.success:
                raise HTTPException(status_code=400, detail=response.message)

            return RateMarketplaceItemResponse(
                success=response.success,
                message=response.message,
                item_id=item_id,
                item_type=item_type,
                rating=rating,
                average_rating=(
                    response.average_rating if hasattr(response, "average_rating") else None
                ),
            )
        elif item_type == MarketplaceItemTypeEnum.WORKFLOW:
            response = await workflow_service.rate_workflow(
                workflow_id=item_id, user_id=user_id, rating=rating
            )

            if not response.success:
                raise HTTPException(status_code=400, detail=response.message)

            return RateMarketplaceItemResponse(
                success=response.success,
                message=response.message,
                item_id=item_id,
                item_type=item_type,
                rating=rating,
                average_rating=(
                    response.average_rating if hasattr(response, "average_rating") else None
                ),
            )
        elif item_type == MarketplaceItemTypeEnum.MCP:
            response = await mcp_service.rate_mcp(mcp_id=item_id, user_id=user_id, rating=rating)

            if not response.success:
                raise HTTPException(status_code=400, detail=response.message)

            return RateMarketplaceItemResponse(
                success=response.success,
                message=response.message,
                item_id=item_id,
                item_type=item_type,
                rating=rating,
                average_rating=(
                    response.average_rating if hasattr(response, "average_rating") else None
                ),
            )
        else:
            raise HTTPException(status_code=400, detail=f"Invalid item type: {item_type}")

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in rate_marketplace_item: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@marketplace_router.post("/use", response_model=UseMarketplaceItemResponse)
async def use_marketplace_item(
    use_data: UseMarketplaceItemRequest,
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Mark a marketplace item (agent, workflow, or MCP) as used.

    This endpoint allows users to mark a marketplace item as used, which increments
    the item's usage count in the system.

    ## Request Body
    - **item_id**: The ID of the item to mark as used
    - **item_type**: The type of item (AGENT, WORKFLOW, MCP)

    ## Response
    Returns a success message and the updated usage count.

    ## Errors
    - 400: Invalid request (invalid item type)
    - 403: Permission denied (not authorized)
    - 404: Item not found
    - 500: Server error
    """
    try:
        # Validate that the user exists
        await validate_user(current_user["user_id"])

        user_id = current_user["user_id"]

        # user_id = "91a237fd-0225-4e02-9e9f-805eff073b07"

        item_id = use_data.item_id
        item_type = use_data.item_type

        # Call the appropriate service based on item type
        if item_type == MarketplaceItemTypeEnum.AGENT:
            response = await agent_service.use_agent(agent_id=item_id, user_id=user_id)

            if not response.success:
                raise HTTPException(status_code=400, detail=response.message)

            return UseMarketplaceItemResponse(
                success=response.success,
                message=response.message,
                item_id=item_id,
                item_type=item_type,
                use_count=response.use_count if hasattr(response, "use_count") else 1,
            )
        elif item_type == MarketplaceItemTypeEnum.WORKFLOW:
            response = await workflow_service.use_workflow(workflow_id=item_id, user_id=user_id)

            if not response.success:
                raise HTTPException(status_code=400, detail=response.message)

            return UseMarketplaceItemResponse(
                success=response.success,
                message=response.message,
                item_id=item_id,
                item_type=item_type,
                use_count=response.use_count if hasattr(response, "use_count") else 1,
            )
        elif item_type == MarketplaceItemTypeEnum.MCP:
            response = await mcp_service.use_mcp(mcp_id=item_id, user_id=user_id)

            if not response.success:
                raise HTTPException(status_code=400, detail=response.message)

            return UseMarketplaceItemResponse(
                success=response.success,
                message=response.message,
                item_id=item_id,
                item_type=item_type,
                use_count=response.use_count if hasattr(response, "use_count") else 1,
            )
        else:
            raise HTTPException(status_code=400, detail=f"Invalid item type: {item_type}")

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in use_marketplace_item: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
