from typing import Optional, List
from pydantic import BaseModel, Field, EmailStr
from datetime import datetime
from enum import Enum
# Source Type Enum
class SourceType(str, Enum):
    GOOGLE_DRIVE = "GOOGLE_DRIVE"
    SLACK = "SLACK"

class OrgOwnerInfo(BaseModel):
    userId: str
    email: str
    fullName: str
    role: str

class OrganisationBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    website_url: Optional[str] = Field(None, max_length=500, alias="websiteUrl")
    industry: Optional[str] = Field(None, max_length=100)

class OrganisationCreate(OrganisationBase):
    pass # Owner ID is derived from the authenticated user token

class OrganisationUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)

class OrganisationResponse(OrganisationBase):
    id: str
    created_by: str = Field(None, max_length=500, alias="createdBy")
    created_at: str = Field(None, max_length=500, alias="createdAt")
    updated_at: str = Field(None, max_length=500, alias="updatedAt")
    
    class Config:
        from_attributes = True  # Pydantic v2 equivalent of orm_mode
        populate_by_name = True  # Pydantic v2 equivalent of allow_population_by_field_name

class UserOrganisationsResponse(OrganisationResponse):
    is_primary: bool
    is_admin: bool

class DeleteOrganisationResponse(BaseModel):
    success: bool
    message: str

class OrganisationListResponse(BaseModel):
    success: bool
    message: str
    organisations: List[UserOrganisationsResponse]
    class Config:
        from_attributes = True
        populate_by_name = True

class OrganisationMemberUpdate(BaseModel):
    user_id: str = Field(..., alias="userIdToAddOrRemove")

    class Config:
        allow_population_by_field_name = True


class InviteStatus(str, Enum):
    PENDING = "pending"
    ACCEPTED = "accepted"
    EXPIRED = "expired"
    DECLINED = "declined"


class InviteBase(BaseModel):
    email: EmailStr = Field(..., description="Email address of the invitee")
    organisation_id: str = Field(..., description="ID of the organisation", alias="organisationId")
    department: Optional[str] = Field(None, description="Department ID or name")
    role: Optional[str] = Field(None, description="Role for the invited user")
    permission: Optional[str] = Field(None, description="Specific permissions for the user")


class InviteCreate(InviteBase):
    pass


class InviteResponse(InviteBase):
    id: str = Field(..., description="Unique ID of the invite")
    created_by: str = Field(..., description="ID of the user who created the invite", alias="createdBy")
    status: InviteStatus = Field(..., description="Current status of the invite")
    created_at: str = Field(..., description="When the invite was created", alias="createdAt")
    updated_at: str = Field(..., description="When the invite was last updated", alias="updatedAt")
    
    class Config:
        from_attributes = True
        populate_by_name = True


class InviteListResponse(BaseModel):
    success: bool
    message: str
    invites: List[InviteResponse]
    total_count: int = Field(..., alias="totalCount")
    page: int
    page_size: int = Field(..., alias="pageSize")
    
    class Config:
        populate_by_name = True
        
class DepartmentBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, description="Name of the department")
    description: Optional[str] = Field(None, max_length=500, description="Description of the department")
    parent_department_id: Optional[str] = Field(None, description="ID of the parent department (for hierarchical departments)")
    visibility: Optional[str] = Field(None, description="Visibility of the department (PUBLIC or PRIVATE)")

class DepartmentCreate(DepartmentBase):
    organisation_id: str = Field(..., description="ID of the organisation this department belongs to", alias="organisationId")
    
    class Config:
        populate_by_name = True

class DepartmentResponse(DepartmentBase):
    id: str = Field(..., description="Unique ID of the department")
    organisation_id: str = Field(..., description="ID of the organisation", alias="organisationId")
    created_by: str = Field(..., description="ID of the user who created the department", alias="createdBy")
    created_at: str = Field(..., description="When the department was created", alias="createdAt")
    updated_at: str = Field(..., description="When the department was last updated", alias="updatedAt")
    member_count: Optional[int] = Field(0, description="Number of members in the department", alias="memberCount")
    agent_count: Optional[int] = Field(0, description="Number of agents in the department", alias="agentCount")
    
    class Config:
        from_attributes = True
        populate_by_name = True

class DepartmentListResponse(BaseModel):
    success: bool
    message: str
    departments: List[DepartmentResponse]
    total_count: int = Field(..., alias="totalCount")
    page: int
    page_size: int = Field(..., alias="pageSize")
    
    class Config:
        populate_by_name = True

class SimpleDepartmentResponse(BaseModel):
    """Simple department response model for use in CreateOrganisationResponse"""
    id: str = Field(..., description="Unique ID of the department")
    name: str = Field(..., description="Name of the department")
    description: str = Field(None, description="Description of the department")
    member_count: int = Field(0, description="Number of members in the department", alias="memberCount")
    agent_count: int = Field(0, description="Number of agents in the department", alias="agentCount")
    visibility: str = Field(None, description="Visibility of the department")
    
    class Config:
        from_attributes = True
        populate_by_name = True

class CreateOrganisationResponse(BaseModel):
    """Response model for organisation creation that includes departments"""
    organisation: OrganisationResponse = Field(..., description="Organisation details")
    departments: List[SimpleDepartmentResponse] = Field([], description="Departments in the organisation")
    message: str = Field(..., description="Response message")
    success: bool = Field(..., description="Whether the request was successful")
    
    class Config:
        from_attributes = True
        populate_by_name = True

class PendingInvite(BaseModel):
    invite_id: str = Field(..., description="Unique ID of the invite", alias="inviteId")
    organisation: OrganisationResponse = Field(..., description="Organisation details for the invite")
    department: str = Field(..., description="Department name for the invite")
    role: str = Field(..., description="Role assigned in the invite")
    permission: str = Field(..., description="Permission level for the invite")
    inviter_id: str = Field(..., description="ID of the user who sent the invite", alias="inviterId")
    inviter_name: str = Field(..., description="Name of the user who sent the invite", alias="inviterName")
    created_at: str = Field(..., description="When the invite was created", alias="createdAt")
    expires_at: str = Field(..., description="When the invite expires", alias="expiresAt")

    class Config:
        from_attributes = True
        populate_by_name = True

class OrganisationInvitesResponse(BaseModel):
    organisations: List[UserOrganisationsResponse] = Field(default=[], description="List of organisations")
    pending_invites: List[PendingInvite] = Field(default=[], description="List of pending invites", alias="pendingInvites")
    personal_space: bool = Field(..., description="Whether the user has personal space", alias="personalSpace")
    has_joined: bool = Field(..., description="Whether the user has joined", alias="hasJoined")
    message: str = Field(..., description="Response message")
    success: bool = Field(..., description="Whether the request was successful")

    class Config:
        from_attributes = True
        populate_by_name = True

class DepartmentUser(BaseModel):
    """Model for a user within a department"""
    id: str = Field(..., description="User ID")
    name: str = Field(..., description="User's full name")
    email: str = Field(..., description="User's email address")
    role: Optional[str] = Field(None, description="User's role in the department")
    permission: Optional[str] = Field(None, description="User's permission level")
    
    class Config:
        from_attributes = True
        populate_by_name = True

class DepartmentUsersResponse(BaseModel):
    """Response model for department users list"""
    success: bool = Field(..., description="Whether the request was successful")
    message: str = Field(..., description="Response message")
    users: List[DepartmentUser] = Field([], description="List of users in the department")
    total_count: int = Field(..., description="Total number of users in the department", alias="totalCount")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of items per page", alias="pageSize")
    dept_name: str = Field(..., description="Department name")
    dept_desc: str = Field(..., description="Department description")
    
    class Config:
        from_attributes = True
        populate_by_name = True
# Schema for invites created by a user (inviter)
class InviterInvite(BaseModel):
    invitee_id: str = Field("", description="ID of the invited user (empty for pending invites)")
    invitee_name: str = Field("", description="Name of the invited user (empty for pending invites)")
    invitee_email: str = Field(..., description="Email of the invited user")
    organisation_id: str = Field(..., description="ID of the organisation", alias="organisationId")
    organisation_name: str = Field(..., description="Name of the organisation", alias="organisationName")
    department: Optional[str] = Field(None, description="Department name")
    role: Optional[str] = Field(None, description="Role in the organisation")
    permission: Optional[str] = Field(None, description="Permission level")
    status: str = Field(..., description="Status of the invite: ACCEPTED or PENDING")
    joined_at: Optional[str] = Field(None, description="When the invite was accepted (for accepted invites)", alias="joinedAt")
    created_at: Optional[str] = Field(None, description="When the invite was created (for pending invites)", alias="createdAt")
    expires_at: Optional[str] = Field(None, description="When the invite expires (for pending invites)", alias="expiresAt")
    
    class Config:
        from_attributes = True
        populate_by_name = True

# Response model for getting invites created by a user
class InviterInvitesResponse(BaseModel):
    invites: List[InviterInvite] = Field([], description="List of invites created by the user")
    message: str = Field(..., description="Response message")
    success: bool = Field(..., description="Whether the operation was successful")
    
    class Config:
        from_attributes = True
        populate_by_name = True
# Source Models
class SourceBase(BaseModel):
    organisation_id: str = Field(..., alias="organisationId")
    type: SourceType
    name: str = Field(..., min_length=1, max_length=100)

class AddSourceRequest(SourceBase):
    credentials_file: str = Field(..., alias="credentialsFile")
    
    class Config:
        from_attributes = True
        populate_by_name = True

class SourceResponse(SourceBase):
    id: str
    created_at: str = Field(..., alias="createdAt")
    updated_at: str = Field(..., alias="updatedAt")
    
    class Config:
        from_attributes = True
        populate_by_name = True

class AddSourceResponse(BaseModel):
    success: bool
    message: str
    source: Optional[SourceResponse] = None
    
    class Config:
        from_attributes = True
        populate_by_name = True

class ListSourcesResponse(BaseModel):
    success: bool
    message: str
    sources: List[SourceResponse] = Field(default=[])
    
    class Config:
        from_attributes = True
        populate_by_name = True

class DeleteSourceRequest(BaseModel):
    source_id: str = Field(..., alias="sourceId")
    
    class Config:
        from_attributes = True
        populate_by_name = True

class DeleteSourceResponse(BaseModel):
    success: bool
    message: str
    
    class Config:
        from_attributes = True
        populate_by_name = True