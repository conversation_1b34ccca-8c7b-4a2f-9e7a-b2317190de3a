"""
Context models for workflow execution.

This module contains models related to workflow execution context.
"""

from typing import Dict, Any, Optional, List, Set
import logging
import asyncio
from enum import Enum, auto


class WorkflowContext:
    """
    Context for workflow execution.

    This class provides a context for workflow execution, including
    node outputs, global variables, and logging.
    """

    def __init__(
        self,
        workflow_id: Optional[str] = None,
        execution_id: Optional[str] = None,
        global_context: Optional[Dict[str, Any]] = None,
    ):
        """
        Initialize the workflow context.

        Args:
            workflow_id: The ID of the workflow being executed.
            execution_id: The ID of the execution.
            global_context: Initial global context variables.
        """
        self.workflow_id = workflow_id
        self.execution_id = execution_id
        self.node_outputs: Dict[str, Dict[str, Any]] = {}
        self.global_context = global_context or {}
        self.current_node_id: Optional[str] = None
        self.visited_nodes: Set[str] = set()
        self.logs: List[str] = []
        self._logger = logging.getLogger("workflow_context")

    def set_current_node(self, node_id: str) -> None:
        """
        Set the current node being executed.

        Args:
            node_id: The ID of the current node.
        """
        self.current_node_id = node_id
        self.visited_nodes.add(node_id)

    def add_node_outputs(self, node_id: str, outputs: Dict[str, Any]) -> None:
        """
        Add outputs from a node to the context.

        Args:
            node_id: The ID of the node.
            outputs: The outputs from the node.
        """
        self.node_outputs[node_id] = outputs

    def get_node_output(self, node_id: str, output_name: str) -> Any:
        """
        Get a specific output from a node.

        Args:
            node_id: The ID of the node.
            output_name: The name of the output.

        Returns:
            The output value, or None if not found.
        """
        node_outputs = self.node_outputs.get(node_id, {})
        return node_outputs.get(output_name)

    def set_global_variable(self, name: str, value: Any) -> None:
        """
        Set a global variable in the context.

        Args:
            name: The name of the variable.
            value: The value of the variable.
        """
        self.global_context[name] = value

    def get_global_variable(self, name: str, default: Any = None) -> Any:
        """
        Get a global variable from the context.

        Args:
            name: The name of the variable.
            default: The default value to return if the variable is not found.

        Returns:
            The value of the variable, or the default value if not found.
        """
        return self.global_context.get(name, default)

    def log(self, message: str) -> None:
        """
        Log a message in the context.

        Args:
            message: The message to log.
        """
        self.logs.append(message)
        self._logger.info(message)

    def get_logs(self) -> List[str]:
        """
        Get all logs from the context.

        Returns:
            A list of log messages.
        """
        return self.logs
