from typing import Any, Dict
from datetime import datetime, timedelta
import grpc
from sqlalchemy.exc import IntegrityError
from sqlalchemy import or_
from sqlalchemy.sql import func
from jose import jwt
from passlib.context import CryptContext
from app.core.config import settings
from app.models.user import User, UserRole
from app.grpc import user_pb2, user_pb2_grpc
from app.db.session import SessionLocal
from app.services.auth_haders import AuthService
from app.utils.kafka.kafka_service import KafkaProducer
from app.utils.redis.redis_service import RedisService
import math
from app.utils.constants.send_email_type_enum import SendEmailTypeEnum
from app.utils.constants.otp_verification_type_enum import OTPVerificationTypeEnum
from sqlalchemy import or_, func  # Import func
from app.utils.secret_manager.secret_manager import EncryptionManager

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

auth_service = AuthService()
secret_manager = EncryptionManager()


class UserFunctions(user_pb2_grpc.UserServiceServicer):
    def __init__(self):
        self.redis_service = RedisService()
        self.kafka_producer = KafkaProducer()

    def get_db(self):
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def updateEmailVerifiedDetails(
        self, request: user_pb2.UpdateEmailVerificationDetails, context: grpc.ServicerContext
    ) -> user_pb2.UpdateEmailVerifiedDetailsResponse:
        """
        Activates the user account and removes the OTP from Redis.

        Args:
            request (user_pb2.UpdateEmailVerificationDetails): Contains token for verification.
            context (grpc.ServicerContext): gRPC context for error handling.

        Returns:
            user_pb2.UpdateEmailVerifiedDetailsResponse: Response with success status and user details.
        """
        try:
            db = self.get_db()

            # Check the validity of the OTP token and retrieve the token object
            token_data = auth_service._check_otp_token_validity(
                request.token, OTPVerificationTypeEnum.EMAIL.value
            )

            token_object = token_data["token_object"]

            # Check if the user exists in the database
            user = db.query(User).filter(User.email == token_object["email"]).first()
            if not user:
                raise ValueError("User not found")

            # If the user is already verified, raise an error
            if user.is_email_verified:
                raise ValueError("Email already verified")

            # Update user verification status
            user.is_email_verified = True

            # Send a welcome email
            self.kafka_producer.send_email_event_unified(
                email_type=SendEmailTypeEnum.WELCOME.value,
                data={
                    "emailId": user.email,
                    "userName": user.full_name,
                    "userId": user.id,
                    "fcmToken": user.fcm_token,
                    "title": "Welcome to our platform!",
                    "link": "https://example.com",
                    "logo": "https://example.com/logo.png",
                    "body": "Welcome to our platform!",
                },
                action=["sendEmail"],
            )

            # Remove the encrypted OTP from Redis
            redis_key = f"{settings.ENV}{OTPVerificationTypeEnum.EMAIL.value}:{user.email}"
            self.redis_service.delete_data_with_key(redis_key)

            # Commit changes to database
            db.commit()
            db.refresh(user)

            return user_pb2.UpdateEmailVerifiedDetailsResponse(
                success=True,
                message="Email verified successfully",
                email=user.email,
                name=user.full_name,
            )
        except ValueError as ve:
            db.rollback()
            if str(ve) == "User not found":
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(str(ve))
            elif str(ve) == "Email already verified":
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details(str(ve))
            elif str(ve) == "OTP is expired":
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details(str(ve))
            elif str(ve) == "OTP is incorrect":
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(str(ve))
            else:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(str(ve))
            return user_pb2.UpdateEmailVerifiedDetailsResponse(success=False, message=str(ve))
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return user_pb2.UpdateEmailVerifiedDetailsResponse(
                success=False, message="Internal server error"
            )
        finally:
            db.close()

    def accessToken(
        self, request: user_pb2.AccessTokenRequest, context: grpc.ServicerContext
    ) -> user_pb2.AccessTokenResponse:

        try:
            db = self.get_db()
            # Decode the refresh token
            payload = jwt.decode(
                request.refreshToken, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM]
            )
            user_id = payload.get("user_id")
            email = payload.get("email")
            role = payload.get("role")

            # Check if refresh token exists in Redis
            redis_key = f"{settings.ENV}_refreshToken_{email}"
            stored_token = self.redis_service.get_data_from_redis(redis_key, "token")

            if stored_token != request.refreshToken:
                raise ValueError("Invalid or expired refresh token")

            # Get user from database
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                raise ValueError("User not found")

            # Generate new access token
            access_token_data = auth_service._generate_access_token(
                {"email": user.email, "id": user.id, "role": user.role}
            )
            return user_pb2.AccessTokenResponse(
                success=True,
                accessToken=access_token_data["token"],
                tokenExpireAt=str(access_token_data["token_expire_at"]),
                accessTokenAge=access_token_data["token_age_timestamp"],  # Send timestamp for refresh
            )
        except ValueError as ve:
            db.rollback()
            if str(ve) == "Invalid or expired refresh token":
                context.set_code(grpc.StatusCode.UNAUTHENTICATED)
                context.set_details(str(ve))
            elif str(ve) == "User not found":
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(str(ve))
        except jwt.ExpiredSignatureError:
            context.set_code(grpc.StatusCode.UNAUTHENTICATED)
            context.set_details("Refresh token expired")
            return user_pb2.AccessTokenResponse(success=False)
        except jwt.JWTError as e:
            context.set_code(grpc.StatusCode.UNAUTHENTICATED)
            context.set_details(str(e))
            return user_pb2.AccessTokenResponse(success=False)
        finally:
            db.close()

    def generateResetPasswordOTP(
        self, request: user_pb2.ResetPasswordOTPRequest, context: grpc.ServicerContext
    ) -> user_pb2.ResetPasswordOTPResponse:
        """
        Generates a reset password OTP for the given email and sends a password reset email.

        Args:
            request (user_pb2.ResetPasswordOTPRequest): Request containing email of the user.
            context (grpc.ServicerContext): gRPC service context.

        Returns:
            user_pb2.ResetPasswordOTPResponse: Response indicating success or failure.
        """
        try:
            db = self.get_db()
            # Find the user by email
            user = db.query(User).filter(User.email == request.email).first()
            if not user:
                raise ValueError("Email not registered")

            # Generate a new 6-digit OTP
            user.otp = auth_service._generate_numeric_otp()
            print(f"Generated OTP: {user.otp}")
            user.otp_timestamp = datetime.now()

            db.flush()

            # Send password reset email
            self.resend_verification_email(
                user,
                OTPVerificationTypeEnum.PASSWORD_RESET.value,
                SendEmailTypeEnum.FORGOT_PASSWORD.value,
                ["sendEmail"],
            )

            # Commit only if everything succeeds
            db.commit()
            db.refresh(user)

            return user_pb2.ResetPasswordOTPResponse(
                success=True,
                message="An email has been sent to your email for password reset",
            )
        except ValueError as ve:
            db.rollback()
            context.set_code(grpc.StatusCode.NOT_FOUND)
            context.set_details(str(ve))
            return user_pb2.ResetPasswordOTPResponse(success=False, message=str(ve))
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return user_pb2.ResetPasswordOTPResponse(success=False, message="Internal server error")
        finally:
            db.close()

    def updatePassword(
        self, request: user_pb2.UpdatePasswordRequest, context: grpc.ServicerContext
    ) -> user_pb2.UpdatePasswordResponse:
        """
        Updates a user's password and removes the OTP from Redis.

        Args:
            request (user_pb2.UpdatePasswordRequest): Request containing new password, confirm password, and token.
            context (grpc.ServicerContext): gRPC service context.

        Returns:
            user_pb2.UpdatePasswordResponse: Response indicating success or failure.
        """
        try:
            db = self.get_db()

            # Check if password and confirmPassword match
            if request.newPassword != request.ConfirmNewPassword:
                raise ValueError("Confirm password and password do not match")

            # Decrypt the OTP token
            token_data = auth_service._check_otp_token_validity(
                request.token, OTPVerificationTypeEnum.PASSWORD_RESET.value
            )
            token_object = token_data["token_object"]

            # Check if user exists
            user = db.query(User).filter(User.email == token_object["email"]).first()
            if not user:
                raise ValueError("User not found")

            # Check if new password is different from the old one
            if auth_service._verify_password(request.newPassword, user.hashed_password):
                raise ValueError("New password cannot be the same as the old password")

            # Encrypt the new password
            encrypted_password = auth_service._get_password_hash(request.newPassword)

            # Update user record
            db.query(User).filter(User.email == token_object["email"]).update(
                {"hashed_password": encrypted_password}
            )
            db.commit()

            # Remove the encrypted OTP from Redis
            redis_key = f"{settings.ENV}{OTPVerificationTypeEnum.PASSWORD_RESET.value}:{user.email}"
            self.redis_service.delete_data_with_key(redis_key)

            return user_pb2.UpdatePasswordResponse(
                success=True,
                message="Password reset successfully",
            )
        except ValueError as ve:
            db.rollback()
            if str(ve) == "Confirm password and password do not match":
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(str(ve))
            elif str(ve) == "User not found":
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(str(ve))
            elif str(ve) == "New password cannot be the same as the old password":
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(str(ve))
            elif str(ve) == "OTP is expired":
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details(str(ve))
            elif str(ve) == "OTP is incorrect":
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(str(ve))
            else:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(str(ve))
            return user_pb2.UpdatePasswordResponse(success=False, message=str(ve))
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return user_pb2.UpdatePasswordResponse(success=False, message="Internal server error")
        finally:
            db.close()

    def resetPassword(
        self, request: user_pb2.ResetPasswordRequest, context: grpc.ServicerContext
    ) -> user_pb2.ResetPasswordResponse:
        """
        Resets a user's password by verifying the current password and setting a new password.

        Args:
            request (user_pb2.ResetPasswordRequest): Request containing user ID, current password, new password, and confirm new password.
            context (grpc.ServicerContext): gRPC service context.

        Returns:
            user_pb2.ResetPasswordResponse: Response indicating success or failure.
        """
        try:
            db = self.get_db()

            # Check if new password and confirm new password match
            if request.newPassword != request.confirmNewPassword:
                raise ValueError("New password and confirm password do not match")

            # Check if user exists
            user = db.query(User).filter(User.id == request.userId).first()
            if not user:
                raise ValueError("User not found")

            # Verify current password
            if not auth_service._verify_password(request.currentPassword, user.hashed_password):
                raise ValueError("Current password is incorrect")

            # Check if new password is different from the old one
            if auth_service._verify_password(request.newPassword, user.hashed_password):
                raise ValueError("New password cannot be the same as the current password")

            # Encrypt the new password
            encrypted_password = auth_service._get_password_hash(request.newPassword)

            # Update user record
            user.hashed_password = encrypted_password
            user.updated_at = datetime.now()
            db.commit()

            return user_pb2.ResetPasswordResponse(
                success=True,
                message="Password changed successfully",
            )
        except ValueError as ve:
            db.rollback()
            if str(ve) == "New password and confirm password do not match":
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(str(ve))
            elif str(ve) == "User not found":
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(str(ve))
            elif str(ve) == "Current password is incorrect":
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(str(ve))
            elif str(ve) == "New password cannot be the same as the current password":
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(str(ve))
            else:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(str(ve))
            return user_pb2.ResetPasswordResponse(success=False, message=str(ve))
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return user_pb2.ResetPasswordResponse(success=False, message="Internal server error")
        finally:
            db.close()

    def getUser(
        self, request: user_pb2.GetUserRequest, context: grpc.ServicerContext
    ) -> user_pb2.UserResponse:
        try:
            db = self.get_db()
            user = db.query(User).filter(User.id == request.userId).first()
            if not user:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("User not found")
                return user_pb2.UserResponse(success=False, message="User not found")

            return user_pb2.UserResponse(
                success=True,
                message="User found",
                user=user_pb2.UserInfo(
                    userId=user.id,
                    email=user.email,
                    fullName=user.full_name,
                    createdAt=user.created_at.isoformat(),
                    updatedAt=user.updated_at.isoformat(),
                    role=user.role.value,
                    company=user.company,
                    department=user.department,
                    jobRole=user.job_role,
                    phoneNumber=user.phone_number,
                    profileImage=user.profile_image,
                    isFirstLogin=user.is_first_login,
                    githubAccessToken=user.github_access_token,
                ),
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error fetching users: {str(e)}")
            return user_pb2.GetAllUsersResponse(
                success=False, message=f"Failed to retrieve users due to an internal error"
            )
        finally:
            db.close()

    def getAllUsers(
        self, request: user_pb2.GetAllUsersRequest, context: grpc.ServicerContext
    ) -> user_pb2.GetAllUsersResponse:
        try:
            db = self.get_db()

            # Get pagination parameters with defaults - fixed to handle zero values properly
            page = max(1, request.page)  # Ensure page is at least 1
            page_size = min(max(1, request.pageSize), 100)  # Ensure page_size is between 1 and 100

            # Handle optional sort fields
            sort_by = "created_at"  # Default value
            if request.HasField("sortBy"):
                sort_by = request.sortBy

            sort_order = "desc"  # Default value
            if request.HasField("sortOrder"):
                sort_order = request.sortOrder

            # Define allowed sort fields
            allowed_sort_fields = {
                "email": User.email,
                "fullName": User.full_name,
                "created_at": User.created_at,
            }

            if sort_by not in allowed_sort_fields:
                raise ValueError(
                    f"Invalid sort field: {sort_by}. Allowed fields: {', '.join(allowed_sort_fields.keys())}"
                )

            # Create base query
            query = db.query(User)

            # Apply filters
            filters_applied = []

            # Handle is_email_verified filter
            if request.HasField("isEmailVerified"):
                query = query.filter(User.is_email_verified == request.isEmailVerified)
                filters_applied.append(f"is_email_verified={request.isEmailVerified}")

            # Handle role filter
            if request.HasField("role"):
                try:
                    role_value = UserRole(request.role)
                    query = query.filter(User.role == role_value)
                    filters_applied.append(f"role={role_value.value}")
                except ValueError:
                    valid_roles = [role.value for role in UserRole]
                    raise ValueError(
                        f"Invalid role value: {request.role}. Valid values: {', '.join(valid_roles)}"
                    )

            # Handle is_active filter
            if request.HasField("isActive"):
                query = query.filter(User.is_active == request.isActive)
                filters_applied.append(f"is_active={request.isActive}")

            # Handle search functionality
            search_term = ""
            if request.HasField("search"):
                search_term = request.search.strip().lower()
                search_term_pattern = f"%{search_term}%"
                query = query.filter(
                    or_(
                        func.lower(User.email).like(search_term_pattern),
                        func.lower(User.full_name).like(search_term_pattern),
                    )
                )
                filters_applied.append(f"search='{search_term}'")

            # Apply sorting
            sort_column = allowed_sort_fields[sort_by]
            if sort_order.lower() == "asc":
                query = query.order_by(sort_column.asc().nulls_last())
            else:
                query = query.order_by(sort_column.desc().nulls_last())

            # Get total count before pagination
            total_users = query.count()
            total_pages = max(1, math.ceil(total_users / page_size))

            if page > total_pages:
                page = total_pages

            # Apply pagination
            offset = (page - 1) * page_size
            users = query.offset(offset).limit(page_size).all()

            # Convert to protobuf
            user_infos = [
                user_pb2.UserInfo(
                    userId=user.id,
                    email=user.email,
                    fullName=user.full_name,
                    createdAt=user.created_at.isoformat(),
                    updatedAt=user.updated_at.isoformat(),
                    role=user.role.value,
                    company=user.company,
                    department=user.department,
                    jobRole=user.job_role,
                )
                for user in users
            ]
            for user in users:
                print(f"USER DATA: {user.company}")
                print(f"USER DATA: {user.department}")
                print(f"USER DATA: {user.job_role}")

            pagination_info = user_pb2.PaginationInfo(
                currentPage=page, totalPages=total_pages, totalItems=total_users, pageSize=page_size
            )

            sorting_info = user_pb2.SortingInfo(sortBy=sort_by, sortOrder=sort_order)

            search_filter_info = user_pb2.SearchFilterInfo(
                appliedFilters=", ".join(filters_applied), searchTerm=search_term
            )

            message = "Retrieved users successfully"
            if filters_applied:
                message += f" with filters: {', '.join(filters_applied)}"

            return user_pb2.GetAllUsersResponse(
                success=True,
                message=message,
                users=user_infos,
                pagination=pagination_info,
                sorting=sorting_info,
                searchFilter=search_filter_info,
            )

        except ValueError as ve:
            db.rollback()
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details(str(ve))
            return user_pb2.UpdatePasswordResponse(success=False, message=str(ve))

        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error fetching users: {str(e)}")
            return user_pb2.GetAllUsersResponse(
                success=False, message=f"Failed to retrieve users: {str(e)}"
            )
        finally:
            db.close()

    def updateUser(
        self, request: user_pb2.UpdateUserRequest, context: grpc.ServicerContext
    ) -> user_pb2.UserResponse:
        db = self.get_db()
        print(f"USER DATA: {request}")
        user = db.query(User).filter(User.id == request.userId).first()
        print(f"USER DATA: {user}")
        if not user:
            context.set_code(grpc.StatusCode.NOT_FOUND)
            context.set_details("User not found")
            return user_pb2.UserResponse(success=False, message="User not found")

        try:
            if request.fullName:
                user.full_name = request.fullName
            if request.email:
                user.email = request.email
            if request.password:
                user.hashed_password = auth_service._get_password_hash(request.password)
            if request.phoneNumber:
                user.phone_number = request.phoneNumber
            if request.profileImage:
                user.profile_image = request.profileImage
            if request.fullName:
                user.full_name = request.fullName

            db.commit()
            db.refresh(user)
            print(f"commited user {user.full_name} {user.phone_number} {user.profile_image}")
            return user_pb2.UserResponse(
                success=True,
                message="User updated successfully",
                user=user_pb2.UserInfo(
                    userId=user.id,
                    email=user.email,
                    fullName=user.full_name,
                    createdAt=user.created_at.isoformat(),
                    updatedAt=user.updated_at.isoformat(),
                    role=user.role.value,
                    phoneNumber=user.phone_number,
                    profileImage=user.profile_image,
                ),
            )
        except IntegrityError:
            context.set_code(grpc.StatusCode.ALREADY_EXISTS)
            context.set_details("Email already exists")
            print("Email already exists")
            return user_pb2.UserResponse(success=False, message="Email already exists")

    def deleteUser(
        self, request: user_pb2.DeleteUserRequest, context: grpc.ServicerContext
    ) -> user_pb2.DeleteUserResponse:
        try:
            db = self.get_db()
            user = db.query(User).filter(User.id == request.userId).first()
            print(f"USER DATA: {user}")
            if not user:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("User not found")
                return user_pb2.DeleteUserResponse(success=False, message="User not found")

            db.delete(user)
            db.commit()
            print("commited")
            return user_pb2.DeleteUserResponse(success=True, message="User deleted successfully")

        except Exception as e:
            print(f"Error deleting user: {e}")
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return user_pb2.DeleteUserResponse(success=False, message="Internal server error")
        finally:
            db.close()

    def listUsers(
        self, request: user_pb2.ListUsersRequest, context: grpc.ServicerContext
    ) -> user_pb2.ListUsersResponse:
        page = request.page
        page_size = request.pageSize
        db = self.get_db()
        total = db.query(User).count()
        users = db.query(User).offset((page - 1) * page_size).limit(page_size).all()

        return user_pb2.ListUsersResponse(
            users=[
                user_pb2.UserInfo(
                    userId=user.id,
                    email=user.email,
                    fullName=user.full_name,
                    createdAt=user.created_at.isoformat(),
                    updatedAt=user.updated_at.isoformat(),
                    role=user.role.value,
                    phoneNumber=user.phone_number,
                    profileImage=user.profile_image,
                )
                for user in users
            ],
            total=total,
            page=page,
            totalPages=(total + page_size - 1) // page_size,
        )

    def searchUsers(
        self, request: user_pb2.SearchUsersRequest, context: grpc.ServicerContext
    ) -> user_pb2.ListUsersResponse:
        page = request.page
        page_size = request.pageSize
        query = request.query
        db = self.get_db()
        search = f"%{query}%"
        total = (
            db.query(User)
            .filter((User.email.ilike(search)) | (User.full_name.ilike(search)))
            .count()
        )

        users = (
            db.query(User)
            .filter((User.email.ilike(search)) | (User.full_name.ilike(search)))
            .offset((page - 1) * page_size)
            .limit(page_size)
            .all()
        )

        return user_pb2.ListUsersResponse(
            users=[
                user_pb2.UserInfo(
                    userId=user.id,
                    email=user.email,
                    fullName=user.full_name,
                    createdAt=user.created_at.isoformat(),
                    updatedAt=user.updated_at.isoformat(),
                    role=user.role.value,
                    phoneNumber=user.phone_number,
                    profileImage=user.profile_image,
                )
                for user in users
            ],
            total=total,
            page=page,
            totalPages=(total + page_size - 1) // page_size,
        )

    def resend_verification_email(
        self, user: User, otp_verification_type: str, email_type: str, action: list
    ) -> Dict[str, str]:
        try:
            # Create a JWT token with OTP and email
            encrypted_otp = jwt.encode(
                {"otp": user.otp, "email": user.email},
                settings.JWT_SECRET_KEY,
                algorithm=settings.JWT_ALGORITHM,
            )

            # Store encrypted OTP in Redis
            redis_key = f"{settings.ENV}{otp_verification_type}:{user.email}"
            self.redis_service.set_data_in_redis_with_ttl(
                redis_key, settings.ENCRYPTED_OTP_VALIDITY, encrypted_otp
            )

            # Determine the frontend route
            frontend_route = (
                "verify-email"
                if email_type == SendEmailTypeEnum.EMAIL_VERIFICATION.value
                else "update-password"
            )

            verification_url = f"{settings.FRONTEND_URL}/{frontend_route}/{encrypted_otp}"

            # Send email via unified kafka method
            self.kafka_producer.send_email_event_unified(
                email_type=email_type,
                data={
                    "emailId": user.email,
                    "userName": user.full_name,
                    "userId": user.id,
                    "fcmToken": user.fcm_token,
                    "otp": verification_url,
                },
                action=action,
            )

            return {"encryptedOtp": encrypted_otp}
        except Exception as e:
            raise Exception("Failed to send email")

    # Helper function to convert User model to UserInfo protobuf message
    def _convert_user_model_to_userinfo(self, user: User) -> user_pb2.UserInfo:
        # Ensure datetime objects are converted to strings as defined in proto
        return user_pb2.UserInfo(
            userId=user.id,
            email=user.email,
            fullName=user.full_name,
            createdAt=user.created_at.isoformat(),
            updatedAt=user.updated_at.isoformat(),
            role=user.role.value,
            # Check for None before assigning optional fields
            company=user.company if user.company is not None else "",
            department=user.department if user.department is not None else "",
        )

    def updateUserProfileDetails(
        self, request: user_pb2.UpdateUserProfileDetailsRequest, context: grpc.ServicerContext
    ) -> user_pb2.UserResponse:
        db = self.get_db()
        try:
            user = db.query(User).filter(User.id == request.userId).first()
            if not user:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("User not found")
                return user_pb2.UserResponse(success=False, message="User not found")

            if request.company:  # Checks if string is not empty
                user.company = request.company
            if request.department:
                user.department = request.department
            if request.jobRole:
                user.job_role = request.jobRole

            # Update the timestamp
            user.updated_at = datetime.utcnow()

            db.commit()
            db.refresh(user)
            print("commited")
            # Convert updated user model to UserInfo protobuf message
            user_info = self._convert_user_model_to_userinfo(user)
            print("user created successfully")
            return user_pb2.UserResponse(
                success=True, message="User profile updated successfully", user=user_info
            )

        except Exception as e:
            db.rollback()
            print(f"Error updating user profile: {e}")  # Add logging
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {e}")
            return user_pb2.UserResponse(success=False, message="Failed to update user profile")
        finally:
            db.close()

    # --- New gRPC Function for Updating Stripe Customer ID ---
    def updateStripeCustomerId(
        self, request: user_pb2.UpdateStripeCustomerIdRequest, context: grpc.ServicerContext
    ) -> user_pb2.UpdateStripeCustomerIdResponse:
        db = self.get_db()
        try:
            user = db.query(User).filter(User.id == request.user_id).first()

            if not user:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"User with ID {request.user_id} not found.")
                return user_pb2.UpdateStripeCustomerIdResponse(
                    success=False, message="User not found"
                )

            # Optional: Check if the stripe_customer_id is already used by another user
            if request.stripe_customer_id:  # Only check if a non-empty ID is provided
                existing_stripe_user = (
                    db.query(User)
                    .filter(
                        User.stripe_customer_id == request.stripe_customer_id,
                        User.id != request.user_id,  # Exclude the current user
                    )
                    .first()
                )
                if existing_stripe_user:
                    context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                    context.set_details(
                        f"Stripe Customer ID {request.stripe_customer_id} is already assigned to another user."
                    )
                    return user_pb2.UpdateStripeCustomerIdResponse(
                        success=False, message="Stripe ID already in use"
                    )

            # Update the stripe customer ID
            user.stripe_customer_id = (
                request.stripe_customer_id if request.stripe_customer_id else None
            )  # Allow setting to null

            db.commit()

            return user_pb2.UpdateStripeCustomerIdResponse(
                success=True, message="Stripe Customer ID updated successfully."
            )

        except Exception as e:
            db.rollback()
            print(f"ERROR updating Stripe Customer ID: {e}")  # Replace with proper logging
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("An internal error occurred.")
            return user_pb2.UpdateStripeCustomerIdResponse(
                success=False, message="Internal server error"
            )
        finally:
            if db:
                db.close()

    def fetchStripeCustomerId(
        self, request: user_pb2.FetchStripeCustomerIdRequest, context: grpc.ServicerContext
    ) -> user_pb2.FetchStripeCustomerIdResponse:
        db = self.get_db()
        try:
            user = db.query(User).filter(User.id == request.user_id).first()

            if not user:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"User with ID {request.user_id} not found.")
                return user_pb2.FetchStripeCustomerIdResponse("Invalid User ID")

            # Return the stripe customer ID (or empty string if it's None/null in DB)
            stripe_id = user.stripe_customer_id if user.stripe_customer_id else ""
            return user_pb2.FetchStripeCustomerIdResponse(stripe_customer_id=stripe_id)

        except Exception as e:
            db.rollback()  # Read operation, rollback might not be strictly necessary but good practice
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("An internal error occurred.")
            # Return response with empty string ID on internal error
            return user_pb2.FetchStripeCustomerIdResponse("Internal Server Error")
        finally:
            if db:
                db.close()

    def validateUser(
        self, request: user_pb2.ValidateUserRequest, context: grpc.ServicerContext
    ) -> user_pb2.ValidateUserResponse:
        db = self.get_db()
        try:
            print(f"Validating user: {request.id}")
            # Query the user by ID
            user = db.query(User).filter(User.id == request.id).first()

            # Check if user exists and is active/verified
            if user and user.is_active and user.is_email_verified:
                user_details = user_pb2.UserDetails(
                    id=user.id, email=user.email, full_name=user.full_name, fcm_token=user.fcm_token
                )
                return user_pb2.ValidateUserResponse(
                    success=True, message="User validated successfully", user=user_details
                )

            # If user doesn't exist or isn't active/verified
            context.set_code(grpc.StatusCode.NOT_FOUND)
            context.set_details("Invalid or inactive user")
            return user_pb2.ValidateUserResponse(
                success=False, message="Invalid or inactive user", user=None
            )
        except Exception as e:
            print(f"Error validating user: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return user_pb2.ValidateUserResponse(
                success=False, message="Internal server error", user=None
            )
        finally:
            db.close()

    def getUsersByIds(
        self, request: user_pb2.GetUsersByIdsRequest, context: grpc.ServicerContext
    ) -> user_pb2.GetUsersByIdsResponse:
        """
        Retrieves multiple users by their IDs in a single database query.

        Args:
            request (user_pb2.GetUsersByIdsRequest): Contains a list of user IDs to retrieve
            context (grpc.ServicerContext): gRPC context for error handling

        Returns:
            user_pb2.GetUsersByIdsResponse: Contains a list of UserDetails objects
        """
        if not request.user_ids:
            return user_pb2.GetUsersByIdsResponse(
                success=False, message="No user IDs provided", users=[]
            )

        db = self.get_db()
        try:
            # Log the request
            print(f"[INFO] Retrieving {len(request.user_ids)} users by IDs")

            # Query all users with the given IDs in a single database query
            users = db.query(User).filter(User.id.in_(request.user_ids)).all()

            # Create a mapping of user IDs to users for quick lookup
            user_map = {user.id: user for user in users}

            if not users:
                return user_pb2.GetUsersByIdsResponse(
                    success=True, message="No users found with the provided IDs", users=[]
                )

            # Convert users to UserDetails objects
            user_details = []
            for user_id in request.user_ids:
                if user_id in user_map:
                    user = user_map[user_id]
                    user_details.append(
                        user_pb2.UserDetails(
                            id=user.id,
                            email=user.email,
                            full_name=user.full_name,
                            fcm_token=user.fcm_token or "",
                        )
                    )

            return user_pb2.GetUsersByIdsResponse(
                success=True,
                message=f"Successfully retrieved {len(user_details)} users",
                users=user_details,
            )

        except Exception as e:
            # Log the error
            print(f"[ERROR] Error retrieving users by IDs: {str(e)}")

            # Set gRPC error details
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error retrieving users: {str(e)}")

            return user_pb2.GetUsersByIdsResponse(
                success=False,
                message=f"Failed to retrieve users due to an internal error",
                users=[],
            )
        finally:
            db.close()

    def updateUserGitToken(
        self, request: user_pb2.UpdateUserGitToken, context: grpc.ServicerContext
    ) -> user_pb2.UserResponse:

        db = self.get_db()
        print("request", request)
        try:
            user = db.query(User).filter(User.id == request.user_id).first()
            if not user:
                raise ValueError(f"User not found with ID: {request.user_id}")

            print(f"DATA RECIVED: {request.github_access_token}")

            # Try to encrypt the token, create encryption key if it doesn't exist
            try:
                github_access_token = secret_manager.encrypt(
                    request.github_access_token, request.user_id
                )
            except ValueError as encryption_error:
                if "No encryption key found" in str(encryption_error):
                    print(f"No encryption key found for user {request.user_id}, creating one...")
                    # Create encryption key for the user
                    secret_id = secret_manager._get_secret_name(request.user_id)
                    secret_manager.create_and_store_user_key(secret_id)
                    print(f"Created new encryption key for user: {request.user_id}")

                    # Now try to encrypt again
                    github_access_token = secret_manager.encrypt(
                        request.github_access_token, request.user_id
                    )
                else:
                    raise encryption_error

            print(f"ENCRYPTED TOKENS: {github_access_token}")
            if not github_access_token:
                raise ValueError("Failed to encrypt tokens")

            user.github_access_token = github_access_token

            db.add(user)
            db.commit()
            db.refresh(user)

            return user_pb2.UserResponse(
                success=True,
                message=f"Successfully updated user token",
            )
        except Exception as e:
            # Log the error
            print(f"[ERROR] Error retrieving users by IDs: {str(e)}")
            db.rollback()
            # Set gRPC error details
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error updating token for user {request.user_id} : {str(e)}")

            return user_pb2.UserResponse(
                success=False,
                message=f"Failed to update user token due to an internal error",
            )
        finally:
            db.close()

    def getUserGitHubToken(
        self, request: user_pb2.GetUserGitHubTokenRequest, context: grpc.ServicerContext
    ) -> user_pb2.GetUserGitHubTokenResponse:
        """
        Retrieves and decrypts the GitHub access token for a user.

        Args:
            request: Contains user_id
            context: gRPC service context

        Returns:
            user_pb2.GetUserGitHubTokenResponse: Success/failure response with decrypted token
        """
        db = self.get_db()
        print(f"[INFO] Getting GitHub token for user: {request.user_id}")

        try:
            # Find the user
            user = db.query(User).filter(User.id == request.user_id).first()
            if not user:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"User not found with ID: {request.user_id}")
                return user_pb2.GetUserGitHubTokenResponse(success=False, message="User not found")

            print(f"[INFO] Found user: {user.email}")

            # Check if user has a GitHub access token
            if not user.github_access_token:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"No GitHub access token found for user: {request.user_id}")
                return user_pb2.GetUserGitHubTokenResponse(
                    success=False,
                    message="No GitHub access token found. Please authenticate with GitHub first.",
                )

            print(f"[INFO] Found encrypted GitHub token for user: {request.user_id}")

            # Decrypt the GitHub access token
            try:
                decrypted_token = secret_manager.decrypt(user.github_access_token, request.user_id)
                print(f"[INFO] Successfully decrypted GitHub token for user: {request.user_id}")

                return user_pb2.GetUserGitHubTokenResponse(
                    success=True,
                    message="Successfully retrieved GitHub access token",
                    access_token=decrypted_token,
                )

            except ValueError as decrypt_error:
                if "No encryption key found" in str(decrypt_error):
                    print(f"[ERROR] No encryption key found for user {request.user_id}. User may need to re-authenticate.")
                    context.set_code(grpc.StatusCode.NOT_FOUND)
                    context.set_details(f"No encryption key found for user: {request.user_id}")
                    return user_pb2.GetUserGitHubTokenResponse(
                        success=False,
                        message="Encryption key not found. Please re-authenticate with GitHub."
                    )
                else:
                    print(f"[ERROR] Failed to decrypt GitHub token for user {request.user_id}: {decrypt_error}")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(f"Failed to decrypt GitHub token: {str(decrypt_error)}")
                    return user_pb2.GetUserGitHubTokenResponse(
                        success=False, message="Failed to decrypt GitHub access token"
                    )
            except Exception as decrypt_error:
                print(
                    f"[ERROR] Failed to decrypt GitHub token for user {request.user_id}: {decrypt_error}"
                )
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"Failed to decrypt GitHub token: {str(decrypt_error)}")
                return user_pb2.GetUserGitHubTokenResponse(
                    success=False, message="Failed to decrypt GitHub access token"
                )

        except Exception as e:
            db.rollback()
            error_msg = f"Error retrieving GitHub token for user {request.user_id}: {str(e)}"
            print(f"[ERROR] {error_msg}")

            # Set gRPC error details
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(error_msg)

            return user_pb2.GetUserGitHubTokenResponse(
                success=False,
                message="Failed to retrieve GitHub access token due to an internal error",
            )
        finally:
            db.close()
