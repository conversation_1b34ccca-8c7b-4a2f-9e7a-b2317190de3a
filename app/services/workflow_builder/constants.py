"""
Application constants for the workflow builder backend.

This module defines constants used throughout the application.
"""

# Component types
COMPONENT_TYPE_MCP = "mcp"
COMPONENT_TYPE_COMPONENT = "component"

# Node types
NODE_TYPE_WORKFLOW_NODE = "WorkflowNode"

# Special component types
START_NODE_TYPE = "StartNode"

# Component categories
CATEGORY_PROCESSING = "Processing"
CATEGORY_CONTROL_FLOW = "Control Flow"
CATEGORY_DATA_INTERACTION = "Data Interaction"
CATEGORY_AI = "AI"
CATEGORY_HITL = "Human-in-the-Loop"
CATEGORY_MCP_MARKETPLACE = "MCP Marketplace"
CATEGORY_UNCATEGORIZED = "Uncategorized"

# Default icons
DEFAULT_ICON = "Cog"
ERROR_ICON = "AlertTriangle"
START_ICON = "Play"
OUTPUT_ICON = "LogOut"

# File paths
WORKFLOWS_DIRECTORY = "workflows"

# API endpoints
API_PREFIX = "/api"
COMPONENTS_ENDPOINT = "/components"
EXECUTE_ENDPOINT = "/execute"
VALIDATE_ENDPOINT = "/validate_workflow"
SAVE_ENDPOINT = "/save_workflow"
SAVE_AND_EXECUTE_ENDPOINT = "/save_and_execute_workflow"

# Response messages
SUCCESS_MESSAGE = "Operation completed successfully"
ERROR_MESSAGE = "An error occurred during the operation"
VALIDATION_ERROR_MESSAGE = "Validation failed"
MISSING_START_NODE_ERROR = "Workflow must have a Start node"
