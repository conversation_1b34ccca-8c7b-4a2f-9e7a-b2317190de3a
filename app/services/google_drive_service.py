import grpc
from typing import List, Optional
from app.core.config import settings
from app.grpc_ import google_drive_pb2, google_drive_pb2_grpc


class GoogleDriveServiceClient:
    def __init__(self):
        self.channel = grpc.insecure_channel(
            f"{settings.ORGANISATION_SERVICE_HOST}:{settings.ORGANISATION_SERVICE_PORT}"
        )
        self.stub = google_drive_pb2_grpc.GoogleDriveServiceStub(self.channel)
    
    async def get_auth_url(self, user_id: str, organisation_id: str):
        """
        Get OAuth URL for Google Drive authorization.
        
        Args:
            user_id: ID of the user requesting authorization
            organisation_id: ID of the organization
            
        Returns:
            The gRPC response containing the auth URL
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = google_drive_pb2.GetAuthUrlRequest(
            user_id=user_id,
            organisation_id=organisation_id
        )
        
        try:
            response = self.stub.getAuthUrl(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
    
    async def connect_drive(self, auth_code: str, state: str):
        """
        Connect Google Drive account.
        
        Args:
            auth_code: OAuth authorization code
            state: OAuth state parameter for verification
            
        Returns:
            The gRPC response from the Google Drive service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = google_drive_pb2.ConnectDriveRequest(
            auth_code=auth_code,
            state=state
        )
        
        try:
            response = self.stub.connectDrive(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
    
    async def disconnect_drive(self, user_id: str):
        """
        Disconnect Google Drive account.
        
        Args:
            user_id: ID of the user
            
        Returns:
            The gRPC response from the Google Drive service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = google_drive_pb2.DisconnectDriveRequest(
            user_id=user_id
        )
        
        try:
            response = self.stub.disconnectDrive(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
    
    async def sync_drive(self, user_id: str, organisation_id: str, full_sync: bool = False):
        """
        Sync Google Drive files and folders.
        
        Args:
            user_id: ID of the user
            organisation_id: ID of the organization
            full_sync: Whether to perform a full sync or incremental
            
        Returns:
            The gRPC response from the Google Drive service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = google_drive_pb2.SyncDriveRequest(
            user_id=user_id,
            organisation_id=organisation_id,
            full_sync=full_sync
        )
        
        try:
            response = self.stub.syncDrive(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
    
    async def list_files(self, user_id: str, folder_id: Optional[str] = None, page: int = 1, page_size: int = 10):
        """
        List Google Drive files and folders.
        
        Args:
            user_id: ID of the user
            folder_id: Optional ID of the folder to list files from
            page: Page number for pagination
            page_size: Number of items per page
            
        Returns:
            The gRPC response from the Google Drive service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = google_drive_pb2.ListFilesRequest(
            user_id=user_id,
            folder_id=folder_id if folder_id else "",
            page=page,
            page_size=page_size
        )
        
        try:
            response = self.stub.listFiles(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
    
    async def get_file_details(self, user_id: str, file_id: str):
        """
        Get file details.
        
        Args:
            user_id: ID of the user
            file_id: ID of the file
            
        Returns:
            The gRPC response from the Google Drive service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = google_drive_pb2.GetFileDetailsRequest(
            user_id=user_id,
            file_id=file_id
        )
        
        try:
            response = self.stub.getFileDetails(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
    
    async def get_folder_by_name(self, user_id: str, folder_name: str):
        """
        Get folder by name and its contents.
        
        Args:
            user_id: ID of the user
            folder_name: Name of the folder
            
        Returns:
            The gRPC response from the Google Drive service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = google_drive_pb2.GetFolderByNameRequest(
            user_id=user_id,
            folder_name=folder_name
        )
        
        try:
            response = self.stub.getFolderByName(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
    
    async def sync_folder_by_name(self, user_id: str, folder_name: str):
        """
        Sync folder by name.
        
        Args:
            user_id: ID of the user
            folder_name: Name of the folder
            
        Returns:
            The gRPC response from the Google Drive service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = google_drive_pb2.SyncFolderByNameRequest(
            user_id=user_id,
            folder_name=folder_name
        )
        
        try:
            response = self.stub.syncFolderByName(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
    
    async def check_file_access(self, user_id: str, file_id: str):
        """
        Check if a user has access to a file.
        
        Args:
            user_id: ID of the user
            file_id: ID of the file
            
        Returns:
            The gRPC response from the Google Drive service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = google_drive_pb2.CheckFileAccessRequest(
            user_id=user_id,
            file_id=file_id
        )
        
        try:
            response = self.stub.checkFileAccess(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
    
    async def search_similar_documents(self, user_id: str, query_text: str, top_k: int = 5, agent_id: Optional[str] = None, organisation_id: str = None, file_ids: Optional[List[str]] = None):
        """
        Search for documents semantically similar to a query.
        
        Args:
            user_id: ID of the user
            query_text: The query text to search for
            top_k: Number of results to return
            agent_id: Optional agent ID to filter results by department access
            organisation_id: Organization ID
            file_ids: Optional list of specific file IDs to search within
            
        Returns:
            The gRPC response from the Google Drive service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = google_drive_pb2.SearchSimilarDocumentsRequest(
            user_id=user_id,
            query_text=query_text,
            top_k=top_k,
            agent_id=agent_id if agent_id else "",
            organisation_id=organisation_id,
            file_ids=file_ids if file_ids else []
        )
        
        try:
            response = self.stub.searchSimilarDocuments(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
    
    async def batch_search_similar_documents(self, user_id: str, query_texts: List[str], top_k: int = 5, agent_id: Optional[str] = None, organisation_id: str = None, file_ids: Optional[List[str]] = None):
        """
        Batch search for documents semantically similar to multiple queries.
        
        Args:
            user_id: ID of the user
            query_texts: List of query texts to search for
            top_k: Number of results to return per query
            agent_id: Optional agent ID to filter results by department access
            organisation_id: Organization ID
            file_ids: Optional list of specific file IDs to search within
            
        Returns:
            The gRPC response from the Google Drive service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = google_drive_pb2.BatchSearchSimilarDocumentsRequest(
            user_id=user_id,
            query_texts=query_texts,
            top_k=top_k,
            agent_id=agent_id if agent_id else "",
            organisation_id=organisation_id,
            file_ids=file_ids if file_ids else []
        )
        
        try:
            response = self.stub.batchSearchSimilarDocuments(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
    
    def _handle_error(self, e: grpc.RpcError):
        """
        Handle gRPC errors and convert them to appropriate HTTP exceptions.
        
        Args:
            e: The gRPC error
            
        Returns:
            HTTPException with appropriate status code and detail
        """
        status_code = e.code()
        details = e.details()
        
        from fastapi import HTTPException
        
        if status_code == grpc.StatusCode.NOT_FOUND:
            raise HTTPException(status_code=404, detail=details)
        elif status_code == grpc.StatusCode.ALREADY_EXISTS:
            raise HTTPException(status_code=409, detail=details)
        elif status_code == grpc.StatusCode.UNAUTHENTICATED:
            raise HTTPException(status_code=401, detail=details)
        elif status_code == grpc.StatusCode.PERMISSION_DENIED:
            raise HTTPException(status_code=403, detail=details)
        elif status_code == grpc.StatusCode.FAILED_PRECONDITION:
            raise HTTPException(status_code=412, detail=details)
        elif status_code == grpc.StatusCode.INVALID_ARGUMENT:
            raise HTTPException(status_code=400, detail=details)
        else:
            raise HTTPException(status_code=500, detail="Internal server error")