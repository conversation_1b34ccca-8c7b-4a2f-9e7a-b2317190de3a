import grpc
from typing import List, Optional
from app.core.config import settings
from app.grpc_ import organisation_pb2, organisation_pb2_grpc
import requests
from typing import List, Optional
from app.services.user_service import UserServiceClient


class OrganisationServiceClient:

    def __init__(self):
        self.channel = grpc.insecure_channel(
            f"{settings.ORGANISATION_SERVICE_HOST}:{settings.ORGANISATION_SERVICE_PORT}"
        )

        self.stub = organisation_pb2_grpc.OrganisationServiceStub(self.channel)
    
    # Organisation related services
    async def create_organisation(self, name: str, user_email: str, user_name: str, website_url: Optional[str] = None, industry: Optional[str] = None, created_by: str = None):
        """
        Create a new organisation in the system.
        
        Args:
            name: The name of the organisation (required)
            website_url: The organisation's website URL (optional)
            industry: The industry sector of the organisation (optional)
            created_by: User ID of the creator (optional, defaults to authenticated user)
            
        Returns:
            The gRPC response from the organisation service
            
        Raises:
            GrpcError: If the gRPC call fails
        """

        # Create the request object with the updated field names
        request = organisation_pb2.CreateOrganisationRequest(
            name=name,
            website_url=website_url if website_url else "",
            industry=industry if industry else "",
            created_by=created_by if created_by else "",
            admin_name=user_name,
            admin_email=user_email,
        )
        
        try:
            response = self.stub.createOrganisation(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_organisation(self, organisation_id: str, requester_user_id: str):
        request = organisation_pb2.GetOrganisationRequest(
            id=organisation_id
        )
        try:
            response = self.stub.getOrganisation(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def invite_user(
        self,
        email: str,
        organisation_id: str,
        created_by: str,
        role: Optional[str] = None,
        department: Optional[str] = None,
        permission: Optional[str] = None
    ):
        """
        Create a new invitation for a user to join an organisation.
        
        Args:
            email: Email address of the user to invite
            organisation_id: ID of the organisation
            created_by: User ID of the person creating the invite
            role: Role to assign to the user (optional)
            department: Department to assign the user to (optional)
            permissions: List of specific permissions for the user (optional)
            
        Returns:
            The gRPC response from the organisation service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        # Create the request object
        request = organisation_pb2.InviteUserRequest(
            email=email,
            organisation_id=organisation_id,
            role=role if role else "",
            department=department if department else "",
            permission=permission if permission else "",
            created_by=created_by
        )

        try:
            response = self.stub.inviteUser(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
        
    async def accept_invite_by_link(self, invite_token: str, auth_user_id: str, user_name: str, auth_user_email: str, accept: bool):
        """
        Accept an invitation using an invite link.
        
        Args:
            invite_link: The encoded invite link
            auth_user_id: User ID from authentication token
            auth_user_email: User email from authentication token
            
        Returns:
            The gRPC response containing the invite details
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        # Create the request object with just the invite link
        # The email will be extracted from auth token and passed to the service
        request = organisation_pb2.AcceptInviteByLinkRequest(
            invite_token=invite_token,
            current_user_email=auth_user_email,
            user_id=auth_user_id,
            user_name=user_name,
            accept=accept
        )

        try:
            response = self.stub.acceptInviteByLink(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
        
    def _handle_error(self, e: grpc.RpcError):
        status_code = e.code()
        details = e.details()

        if status_code == grpc.StatusCode.NOT_FOUND:
            from fastapi import HTTPException

            raise HTTPException(status_code=404, detail=details)
        elif status_code == grpc.StatusCode.ALREADY_EXISTS:
            from fastapi import HTTPException

            raise HTTPException(status_code=409, detail=details)
        elif status_code == grpc.StatusCode.UNAUTHENTICATED:
            from fastapi import HTTPException

            raise HTTPException(status_code=401, detail=details)
        elif status_code == grpc.StatusCode.PERMISSION_DENIED:
            from fastapi import HTTPException

            raise HTTPException(status_code=403, detail=details)
        elif status_code == grpc.StatusCode.FAILED_PRECONDITION:
            from fastapi import HTTPException

            raise HTTPException(status_code=412, detail=details)

        elif status_code == grpc.StatusCode.INVALID_ARGUMENT:
            from fastapi import HTTPException

            raise HTTPException(status_code=400, detail=details)
        else:
            from fastapi import HTTPException

            raise HTTPException(status_code=500, detail="Internal server error")
            
    async def get_user_organisations(self, user_email: str):
        """
        Fetch all organisations that the user belongs to.
        
        Args:
            user_id: ID of the user to get organisations for
            
        Returns:
            UserOrganisationsResponse containing all organisations the user belongs to,
            with information about primary organisation and admin status
            
        Raises:
            GrpcError: If the gRPC call fails
        """

        request = organisation_pb2.GetUserOrganisationsRequest(
            user_id=user_email
        )
        
        try:
            response = self.stub.getUserOrganisations(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
            
    async def create_department(
        self,
        organisation_id: str,
        name: str,
        description: Optional[str] = None,
        parent_department_id: Optional[str] = None,
        created_by: str = None,
        visibility: Optional[str] = None
    ):
        """
        Create a new department within an organisation.
        
        Args:
            organisation_id: ID of the organisation the department belongs to
            name: Name of the department
            description: Optional description of the department
            parent_department_id: Optional ID of the parent department (for hierarchical departments)
            created_by: User ID of the creator
            visibility: Optional visibility setting ("PUBLIC" or "PRIVATE")
            
        Returns:
            The gRPC response from the organisation service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        # Create the request object
        request = organisation_pb2.CreateDepartmentRequest(
            organisation_id=organisation_id,
            name=name,
            description=description if description else "",
            parent_department_id=parent_department_id if parent_department_id else "",
            created_by=created_by if created_by else "",
            visibility=visibility if visibility else ""
        )
        
        try:
            response = self.stub.createDepartment(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
            
    async def list_departments(
        self,
        organisation_id: str,
        user_id: str,
        page: int = 1,
        page_size: int = 10,
        search_term: Optional[str] = None,
        department_id: Optional[str] = None
    ):
        """
        List departments in an organisation with optional filtering and pagination.
        
        Args:
            organisation_id: ID of the organisation to list departments for
            page: Page number for pagination (starts at 1)
            page_size: Number of items per page
            search_term: Optional search term to filter departments by name or description
            department_id: Optional specific department ID to filter by
            
        Returns:
            The gRPC response containing the list of departments
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        # Create the request object
        request = organisation_pb2.ListDepartmentsRequest(
            organisation_id=organisation_id,
            page=page,
            page_size=page_size,
            user_id=user_id,
            search_term=search_term if search_term else "",
            department_id=department_id if department_id else ""
        )
        
        try:
            response = self.stub.listDepartments(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
            
    async def getDepartmentUsers(self, org_id, dept_id=None, page=1, page_size=10):
        """
        Get users belonging to a specific department or all users in an organisation, with pagination.
        
        This method can be called in two ways:
        1. As a gRPC service method: getDepartmentUsers(request, context)
        2. As an internal method: getDepartmentUsers(org_id, dept_id=None, page=1, page_size=10)
        
        Args:
            org_id: Either a GetDepartmentUsersRequest (gRPC) or a string org_id (internal)
            dept_id_or_context: Either a grpc.ServicerContext (gRPC) or a string dept_id (internal, can be None)
            page: Page number (1-based, default: 1) - only used for internal method
            page_size: Number of items per page (default: 10) - only used for internal method
            
        Returns:
            When called as gRPC method: GetDepartmentUsersResponse
            When called internally: Tuple of (users_list, total_count, page, page_size)
        """ 
        request = organisation_pb2.GetDepartmentUsersRequest(
                organisation_id=org_id,
                department_id=dept_id or "",  # Convert None to empty string for protobuf
                page=page,
                page_size=page_size
            )      
        try:
            # Make the gRPC call
            response = self.stub.getDepartmentUsers(request)
            # For internal calls, convert protobuf to dict efficiently
            users = [
                {
                    'id': proto_user.id,
                    'name': proto_user.name,
                    'email': proto_user.email,
                    'role': proto_user.role,
                    'permission': proto_user.permission
                }
                for proto_user in response.users
            ]
            
            return (users, response.total_count, response.page, response.page_size, response.department_name, response.department_desc)
            
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_inviter_invites(self, user_id: str, organisation_id: str, invite_type: str):
            """
            Fetch all invites created by a specific user (inviter).
            Can return either accepted invites (from Neo4j) or pending invites (from PostgreSQL).
            
            Args:
                user_id: ID of the user who created the invites
                invite_type: Type of invites to fetch ("ACCEPTED" or "PENDING")
                
            Returns:
                The gRPC response containing the list of invites
                
            Raises:
                GrpcError: If the gRPC call fails
            """
            # Create the request object
            request = organisation_pb2.ListInviterInvitesRequest(
                user_id=user_id,
                organisation_id=organisation_id,
                type=invite_type
            )
            
            try:
                response = self.stub.getInviterInvites(request)
                return response
            except grpc.RpcError as e:
                raise self._handle_error(e)
    async def add_source(
        self,
        organisation_id: str,
        source_type: int,  # SourceType enum value (0=GOOGLE_DRIVE, 1=SLACK)
        name: str,
        credentials_file: str
    ):
        """
        Add a new source with credentials to an organisation.
        
        Args:
            organisation_id: ID of the organisation
            source_type: Type of source (0=GOOGLE_DRIVE, 1=SLACK)
            name: Name/identifier for the source
            credentials_file: JSON string containing the credentials
            
        Returns:
            The gRPC response from the organisation service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = organisation_pb2.AddSourceRequest(
            organisation_id=organisation_id,
            type=source_type,
            name=name,
            credentials_file=credentials_file
        )
        
        try:
            response = self.stub.addSource(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_sources(self, organisation_id: str):
        """
        List all sources for an organisation.
        
        Args:
            organisation_id: ID of the organisation to list sources for
            
        Returns:
            The gRPC response containing the list of sources
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = organisation_pb2.ListSourcesRequest(
            organisation_id=organisation_id
        )
        
        try:
            response = self.stub.listSources(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def delete_source(self, source_id: str, user_id: str):
        """
        Delete a source from an organisation.
        
        Args:
            source_id: ID of the source to delete
            user_id: ID of the user performing the deletion (must be admin)
            
        Returns:
            The gRPC response from the organisation service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = organisation_pb2.DeleteSourceRequest(
            source_id=source_id,
            user_id=user_id
        )
        
        try:
            response = self.stub.deleteSource(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)