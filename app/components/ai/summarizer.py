from typing import Dict, Any, List, ClassVar
import importlib

from app.components.ai.base_agent_component import (
    BaseAgentComponent,
)
from app.models.workflow_builder.components import InputBase, HandleInput, StringInput, IntInput
from app.models.workflow_builder.components import Output


class Summarizer(BaseAgentComponent):
    """
    Creates a concise summary of a longer piece of text.

    This component takes a longer text and generates a shorter summary
    that captures the key points.
    """

    name: ClassVar[str] = "Summarizer"
    display_name: ClassVar[str] = "Text Summarizer"
    description: ClassVar[str] = "Creates a concise summary of a longer piece of text."

    icon: ClassVar[str] = "FileText"

    # Define component-specific inputs
    component_inputs: ClassVar[List[InputBase]] = [
        # Text to summarize - connection handle
        HandleInput(
            name="text_handle",
            display_name="Text to Summarize",
            required=True,
            is_handle=True,
            input_types=["string", "Any"],
            info="Connect the text that needs to be condensed.",
        ),
        # Text to summarize - direct input in inspector
        StringInput(
            name="text",
            display_name="Text to Summarize (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="The full text that needs to be condensed. Used if no connection is provided.",
        ),
        # Max length - direct input in inspector
        IntInput(
            name="max_length",
            display_name="Max Summary Length",
            required=False,
            is_handle=False,
            value=200,
            info="Maximum length of the summary in words. Use 0 for automatic length.",
        ),
    ]

    # Combine with model client configuration inputs from BaseAgentComponent
    inputs: ClassVar[List[InputBase]] = [
        input_def
        for input_def in BaseAgentComponent.inputs
        if input_def.name in ["model_provider", "base_url", "api_key", "model_name"]
    ] + component_inputs

    outputs: ClassVar[List[Output]] = [
        Output(name="summary", display_name="Summary", output_type="string"),
        Output(name="error", display_name="Error", output_type="str"),
    ]

    def _check_openai_installed(self) -> bool:
        """
        Checks if the openai package is installed.

        Returns:
            True if openai is installed, False otherwise.
        """
        try:
            importlib.import_module("openai")
            return True
        except ImportError:
            return False

    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the Summarizer.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Summarizes the provided text.

        Args:
            **kwargs: Contains the input values:
                - text: The text to summarize
                - max_length: Maximum length of the summary in words
                - api_key: API key for the LLM service
                - model_name: Model to use
                - temperature: Temperature for generation

        Returns:
            A dictionary with:
                - summary: The generated summary
                - error: An error message if the operation failed
        """
        print(
            f"WARNING: Using legacy build method for {self.name}. Please update to use execute method."
        )
        print(f"Executing {self.name}...")

        # Check if openai is installed
        if not self._check_openai_installed():
            return {
                "error": "The openai package is required but not installed. Please install it with 'pip install openai'."
            }

        # Get inputs - prioritize handle inputs over direct inputs
        model_provider = kwargs.get("model_provider", "OpenAI")
        base_url = kwargs.get("base_url", "")
        api_key = kwargs.get("api_key")
        model_name = kwargs.get("model_name", "gpt-3.5-turbo")
        temperature = kwargs.get("temperature", 0.5)

        text_handle = kwargs.get("text_handle")
        text_direct = kwargs.get("text")
        max_length = kwargs.get("max_length", 200)

        # Process inputs - prioritize handle inputs over direct inputs
        text = text_handle if text_handle is not None else text_direct

        # Validate inputs
        if not text:
            return {
                "error": "Text to summarize is missing. Please connect text or provide it directly."
            }
        if not api_key:
            return {"error": "API key is required."}

        try:
            # Import openai
            import openai

            # Set API key and base URL if provided
            openai.api_key = api_key

            # Set base URL if provided and using custom provider
            if model_provider == "Custom" and base_url:
                openai.api_base = base_url
            elif model_provider == "Azure OpenAI":
                # For Azure, we need to set the API type and version
                openai.api_type = "azure"
                openai.api_version = "2023-05-15"
                if base_url:
                    openai.api_base = base_url

            # Create system prompt for summarization
            system_prompt = "You are a text summarization assistant. Create a concise summary of the provided text that captures the key points."

            # Add length constraint if specified
            if max_length > 0:
                system_prompt += f" The summary should be no longer than {max_length} words."

            # Create user prompt
            user_prompt = f"Text to summarize: {text}"

            # Make API call
            response = openai.ChatCompletion.create(
                model=model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                temperature=temperature,
            )

            # Extract content from response
            summary = response.choices[0].message.content.strip()

            print(f"  Summarization completed successfully.")
            return {"summary": summary}

        except Exception as e:
            error_msg = f"Error summarizing text: {str(e)}"
            print(f"  {error_msg}")
            return {"error": error_msg}
