from typing import Dict, Any, List, ClassVar, Optional
from abc import ABC, abstractmethod
import os
import asyncio
import importlib

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    HandleInput,
    PasswordInput,
    StringInput,
    DictInput,
    FloatInput,
    DropdownInput,
    CredentialInput,
    BoolInput,
    InputVisibilityRule,
)
from app.models.workflow_builder.components import Output


from app.utils.workflow_builder.input_helpers import create_dual_purpose_input


class BaseAgentComponent(BaseNode, ABC):
    """
    Base class for AI components.

    This is an abstract base class that provides common functionality for AI components.
    Concrete AI components should inherit from this class and implement the build method.
    """

    # These should be overridden by subclasses
    name: ClassVar[str] = "BaseAgentComponent"
    display_name: ClassVar[str] = "Base AI Agent/Module"
    description: ClassVar[str] = "Base class for AI components."

    category: ClassVar[str] = "AI"
    icon: ClassVar[str] = "BrainCircuit"

    # Flag to indicate this is an abstract base class that shouldn't be displayed in the UI
    is_abstract: ClassVar[bool] = True

    # Add a debug print to see if this class is being loaded
    print("DEBUG: BaseAgentComponent class loaded with is_abstract =", is_abstract)

    inputs: ClassVar[List[InputBase]] = [
        # Model Client Configuration
        DropdownInput(
            name="model_provider",
            display_name="Model Provider",
            options=[
                "OpenAI",
                "Azure OpenAI",
                "Anthropic",
                "Claude",
                "Google",
                "Gemini",
                "Mistral",
                "Ollama",
                "Custom",
            ],
            value="OpenAI",
            info="The AI model provider to use.",
        ),
        StringInput(
            name="base_url",
            display_name="Base URL",
            required=False,
            is_handle=False,
            value="",
            info="Base URL for the API (leave empty for default provider URL).",
            visibility_rules=[
                InputVisibilityRule(field_name="model_provider", field_value="Custom"),
                InputVisibilityRule(field_name="model_provider", field_value="Azure OpenAI"),
                InputVisibilityRule(field_name="model_provider", field_value="Ollama"),
            ],
            visibility_logic="OR",
        ),
        CredentialInput(
            name="api_key",
            display_name="API Key",
            credential_type="api_key",
            info="API key for the model provider. Can be entered directly or referenced from secure storage.",
        ),
        # Model selection - dropdown with provider-specific options
        DropdownInput(
            name="model_name",
            display_name="Model",
            options=[],  # Will be populated dynamically based on provider
            value="",
            info="Select the model to use.",
        ),
        # Temperature - direct input in inspector
        FloatInput(
            name="temperature",
            display_name="Temperature",
            required=False,
            is_handle=False,
            value=0.7,
            info="Controls randomness: 0 is deterministic, higher values are more random.",
        ),
        # Streaming option
        BoolInput(
            name="stream",
            display_name="Stream Response",
            value=False,
            info="Enable streaming for real-time responses. Note: Streaming requires frontend support.",
        ),
        # Token counting and cost estimation
        BoolInput(
            name="enable_token_counting",
            display_name="Enable Token Counting",
            value=True,
            info="Count tokens and estimate costs for API calls.",
            advanced=True,
        ),
        # Input text - single input that can be both connected and directly edited
        create_dual_purpose_input(
            name="input_text",
            display_name="Input Text",
            input_type="string",
            required=False,
            info="Text input for the AI component. Can be connected from another node or entered directly.",
            input_types=["string", "Any"],
        ),
        # Input data - single input that can be both connected and directly edited
        create_dual_purpose_input(
            name="input_data",
            display_name="Input Data",
            input_type="dict",
            required=False,
            info="Structured data input for the AI component. Can be connected from another node or entered directly.",
            input_types=["dict", "Any"],
            value={},
        ),
    ]

    outputs: ClassVar[List[Output]] = [
        Output(name="result", display_name="Result", output_type="Any"),
        Output(name="streaming_chunk", display_name="Streaming Chunk", output_type="string"),
        Output(name="token_usage", display_name="Token Usage", output_type="dict"),
        Output(name="cost_estimate", display_name="Cost Estimate", output_type="dict"),
        Output(name="error", display_name="Error", output_type="str"),
    ]

    def _get_model_options(self, provider: str) -> List[str]:
        """
        Get model options for the specified provider.

        Args:
            provider: The model provider name.

        Returns:
            A list of model options for the provider.
        """
        # Default model options by provider
        provider_models = {
            "OpenAI": ["gpt-4o", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo"],
            "Azure OpenAI": ["gpt-4", "gpt-3.5-turbo"],
            "Anthropic": [
                "claude-3-opus",
                "claude-3-sonnet",
                "claude-3-haiku",
                "claude-2",
            ],
            "Claude": [
                "claude-3-opus",
                "claude-3-sonnet",
                "claude-3-haiku",
                "claude-2",
            ],
            "Google": ["gemini-pro", "gemini-ultra"],
            "Gemini": ["gemini-pro", "gemini-ultra"],
            "Mistral": ["mistral-large", "mistral-medium", "mistral-small"],
            "Ollama": ["llama3", "llama2", "mistral", "mixtral", "phi3", "gemma"],
        }

        return provider_models.get(provider, ["custom-model"])

    def _estimate_tokens(self, text: str) -> int:
        """
        Estimate the number of tokens in the text.

        Args:
            text: The text to estimate tokens for.

        Returns:
            Estimated number of tokens.
        """
        # Simple estimation: ~4 characters per token
        return len(text) // 4

    def _estimate_cost(self, token_usage: Dict[str, int], model_name: str) -> Dict[str, float]:
        """
        Estimate the cost of the API call based on token usage.

        Args:
            token_usage: Dictionary with prompt_tokens, completion_tokens, and total_tokens.
            model_name: The name of the model used.

        Returns:
            Dictionary with cost estimates.
        """
        # Default cost rates per 1000 tokens (USD)
        cost_rates = {
            # OpenAI models
            "gpt-4o": {"input": 0.005, "output": 0.015},
            "gpt-4-turbo": {"input": 0.01, "output": 0.03},
            "gpt-4": {"input": 0.03, "output": 0.06},
            "gpt-3.5-turbo": {"input": 0.0005, "output": 0.0015},
            # Anthropic models
            "claude-3-opus": {"input": 0.015, "output": 0.075},
            "claude-3-sonnet": {"input": 0.003, "output": 0.015},
            "claude-3-haiku": {"input": 0.00025, "output": 0.00125},
            "claude-2": {"input": 0.008, "output": 0.024},
            # Google models
            "gemini-pro": {"input": 0.00025, "output": 0.0005},
            "gemini-ultra": {"input": 0.0005, "output": 0.0015},
            # Mistral models
            "mistral-large": {"input": 0.008, "output": 0.024},
            "mistral-medium": {"input": 0.0027, "output": 0.0081},
            "mistral-small": {"input": 0.0002, "output": 0.0006},
        }

        # Get cost rates for the model, default to gpt-3.5-turbo rates if not found
        rates = cost_rates.get(model_name, {"input": 0.0005, "output": 0.0015})

        # Calculate costs
        prompt_tokens = token_usage.get("prompt_tokens", 0)
        completion_tokens = token_usage.get("completion_tokens", 0)

        prompt_cost = (prompt_tokens / 1000) * rates["input"]
        completion_cost = (completion_tokens / 1000) * rates["output"]
        total_cost = prompt_cost + completion_cost

        return {
            "prompt_cost": prompt_cost,
            "completion_cost": completion_cost,
            "total_cost": total_cost,
            "currency": "USD",
        }

    @abstractmethod
    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the BaseAgentComponent.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Abstract method that must be implemented by concrete AI components.

        Args:
            **kwargs: Input values for the component.

        Returns:
            A dictionary with the component's outputs.
        """
        print(
            f"WARNING: Using legacy build method for {self.name}. Please update to use execute method."
        )
        raise NotImplementedError("Subclasses must implement build method")
