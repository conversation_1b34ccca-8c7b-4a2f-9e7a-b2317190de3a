from typing import Dict, Any, List, ClassVar
import importlib
import os

from app.components.ai.base_agent_component import (
    BaseAgentComponent,
)


import asyncio
from app.models.workflow_builder.components import (
    InputBase,
    HandleInput,
    CredentialInput,
    DropdownInput,
    StringInput,
    IntInput,
    FloatInput,
    InputVisibilityRule,
    ListInput,
)
from app.models.workflow_builder.components import Output


class OpenAIModule(BaseAgentComponent):
    """
    Direct OpenAI API call.

    This component makes direct calls to the OpenAI API for text generation
    using either the ChatCompletion or Completion endpoints.
    """

    name: ClassVar[str] = "OpenAIModule"
    display_name: ClassVar[str] = "OpenAI Call"
    description: ClassVar[str] = "Direct OpenAI API call."

    icon: ClassVar[str] = "OpenAI"
    is_abstract: ClassVar[bool] = False  # Explicitly set to False to ensure it's not abstract

    # Use the component logger to log class loading

    print("DEBUG: OpenAIModule class loaded")

    # Define component-specific inputs
    component_inputs: ClassVar[List[InputBase]] = [
        DropdownInput(
            name="endpoint",
            display_name="API Endpoint",
            options=["ChatCompletion", "Completion (Legacy)"],
            value="ChatCompletion",
            info="The OpenAI API endpoint to use.",
        ),
        # Messages - connection handle
        HandleInput(
            name="messages_handle",
            display_name="Messages",
            is_handle=True,
            input_types=["list"],
            info="Connect a list of message objects from another node.",
            visibility_rules=[
                InputVisibilityRule(field_name="endpoint", field_value="ChatCompletion")
            ],
        ),
        # Messages - direct input in inspector
        ListInput(
            name="messages",
            display_name="Messages (Direct)",
            required=False,
            is_handle=False,
            value=[],
            info="List of message objects for ChatCompletion. Each message should have 'role' and 'content' keys. Used if no connection is provided.",
            visibility_rules=[
                InputVisibilityRule(field_name="endpoint", field_value="ChatCompletion")
            ],
        ),
        # Prompt - connection handle
        HandleInput(
            name="prompt_handle",
            display_name="Prompt",
            is_handle=True,
            input_types=["string"],
            info="Connect a text prompt from another node.",
            visibility_rules=[
                InputVisibilityRule(field_name="endpoint", field_value="Completion (Legacy)")
            ],
        ),
        # Prompt - direct input in inspector
        StringInput(
            name="prompt",
            display_name="Prompt (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="Text prompt for Completion API. Used if no connection is provided.",
            visibility_rules=[
                InputVisibilityRule(field_name="endpoint", field_value="Completion (Legacy)")
            ],
        ),
        IntInput(
            name="max_tokens",
            display_name="Max Tokens",
            value=1000,
            info="Maximum number of tokens to generate.",
        ),
        FloatInput(
            name="temperature",
            display_name="Temperature",
            value=0.7,
            info="Controls randomness: 0 is deterministic, higher values are more random.",
        ),
    ]

    # Combine with model client configuration inputs from BaseAgentComponent
    inputs: ClassVar[List[InputBase]] = [
        input_def
        for input_def in BaseAgentComponent.inputs
        if input_def.name in ["model_provider", "base_url", "api_key", "model_name"]
    ] + component_inputs

    outputs: ClassVar[List[Output]] = [
        Output(name="response", display_name="API Response", output_type="dict"),
        Output(name="content", display_name="Generated Content", output_type="string"),
        Output(name="usage", display_name="Token Usage", output_type="dict"),
        Output(name="error", display_name="Error", output_type="str"),
    ]

    def _check_openai_installed(self) -> bool:
        """
        Checks if the openai package is installed.

        Returns:
            True if openai is installed, False otherwise.
        """
        try:
            importlib.import_module("openai")
            return True
        except ImportError:
            return False

    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the OpenAIModule.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.
        """
        print(
            f"WARNING: Using legacy build method for {self.name}. Please update to use execute method."
        )
        # This method is kept for backward compatibility

        # Check if openai is installed
        if not self._check_openai_installed():
            return {
                "error": "The openai package is required but not installed. Please install it with 'pip install openai'."
            }

        # Get inputs - prioritize handle inputs over direct inputs
        model_provider = kwargs.get("model_provider", "OpenAI")
        base_url = kwargs.get("base_url", "")
        api_key = kwargs.get("api_key")
        endpoint = kwargs.get("endpoint", "ChatCompletion")
        model_name = kwargs.get("model_name", "gpt-3.5-turbo")
        messages_handle = kwargs.get("messages_handle")
        messages_direct = kwargs.get("messages", [])
        prompt_handle = kwargs.get("prompt_handle")
        prompt_direct = kwargs.get("prompt", "")
        max_tokens = kwargs.get("max_tokens", 1000)
        temperature = kwargs.get("temperature", 0.7)

        # Process inputs - prioritize handle inputs over direct inputs
        messages = messages_handle if messages_handle is not None else messages_direct
        prompt = prompt_handle if prompt_handle is not None else prompt_direct

        # Use environment variable if API key not provided
        if not api_key:
            api_key = os.environ.get("OPENAI_API_KEY")
            if not api_key:
                return {
                    "error": "API key is required. Please provide it or set the OPENAI_API_KEY environment variable."
                }

        try:
            # Import openai
            import openai

            # Set API key and base URL if provided
            openai.api_key = api_key

            # Set base URL if provided and using custom provider
            if model_provider == "Custom" and base_url:
                openai.api_base = base_url
            elif model_provider == "Azure OpenAI":
                # For Azure, we need to set the API type and version
                openai.api_type = "azure"
                openai.api_version = "2023-05-15"
                if base_url:
                    openai.api_base = base_url

            # Make API call based on endpoint
            if endpoint == "ChatCompletion":
                # Validate messages
                if not messages:
                    # Create a default message if none provided
                    messages = [{"role": "user", "content": "Hello"}]

                # Ensure messages have the correct format
                for msg in messages:
                    if not isinstance(msg, dict) or "role" not in msg or "content" not in msg:
                        return {
                            "error": "Each message must be a dictionary with 'role' and 'content' keys."
                        }

                # Make ChatCompletion API call
                response = openai.ChatCompletion.create(
                    model=model_name,
                    messages=messages,
                    max_tokens=max_tokens,
                    temperature=temperature,
                )

                # Extract content from response
                content = response.choices[0].message.content

            elif endpoint == "Completion (Legacy)":
                # Validate prompt
                if not prompt:
                    return {"error": "Prompt is required for Completion API."}

                # Make Completion API call
                response = openai.Completion.create(
                    model=model_name,
                    prompt=prompt,
                    max_tokens=max_tokens,
                    temperature=temperature,
                )

                # Extract content from response
                content = response.choices[0].text

            else:
                return {"error": f"Unsupported endpoint: {endpoint}"}

            # Extract usage information
            usage = response.usage.to_dict() if hasattr(response, "usage") else {}

            print(f"  API call completed successfully.")
            return {"response": response, "content": content, "usage": usage}

        except Exception as e:
            error_msg = f"Error calling OpenAI API: {str(e)}"
            print(f"  {error_msg}")
            return {"error": error_msg}
