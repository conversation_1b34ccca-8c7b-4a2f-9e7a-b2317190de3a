from typing import Dict, Any, List, ClassVar
import importlib

from app.components.ai.base_agent_component import (
    BaseAgentComponent,
)
from app.models.workflow_builder.components import InputBase, HandleInput, StringInput
from app.models.workflow_builder.components import Output


class QuestionAnswerModule(BaseAgentComponent):
    """
    Answers questions based on a provided context document.

    This component takes a question and a context document, then generates
    an answer based only on the information in the context.
    """

    name: ClassVar[str] = "QuestionAnswerModule"
    display_name: ClassVar[str] = "Question Answering"
    description: ClassVar[str] = "Answers questions based on a provided context document."

    icon: ClassVar[str] = "HelpCircle"

    # Define component-specific inputs
    component_inputs: ClassVar[List[InputBase]] = [
        # Question - connection handle
        HandleInput(
            name="question_handle",
            display_name="Question",
            required=True,
            is_handle=True,
            input_types=["string"],
            info="Connect the question to be answered.",
        ),
        # Question - direct input in inspector
        StringInput(
            name="question",
            display_name="Question (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="The question to be answered. Used if no connection is provided.",
        ),
        # Context - connection handle
        HandleInput(
            name="context_handle",
            display_name="Context Document",
            required=True,
            is_handle=True,
            input_types=["string", "Any"],
            info="Connect the context document containing the information needed to answer the question.",
        ),
        # Context - direct input in inspector
        StringInput(
            name="context",
            display_name="Context Document (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="The text containing the information needed to answer the question. Used if no connection is provided.",
        ),
    ]

    # Combine with model client configuration inputs from BaseAgentComponent
    inputs: ClassVar[List[InputBase]] = [
        input_def
        for input_def in BaseAgentComponent.inputs
        if input_def.name in ["model_provider", "base_url", "api_key", "model_name"]
    ] + component_inputs

    outputs: ClassVar[List[Output]] = [
        Output(name="answer", display_name="Answer", output_type="string"),
        Output(name="error", display_name="Error", output_type="str"),
    ]

    def _check_openai_installed(self) -> bool:
        """
        Checks if the openai package is installed.

        Returns:
            True if openai is installed, False otherwise.
        """
        try:
            importlib.import_module("openai")
            return True
        except ImportError:
            return False

    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the QuestionAnswerModule.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Answers a question based on the provided context.

        Args:
            **kwargs: Contains the input values:
                - question: The question to be answered
                - context: The context document containing the information
                - api_key: API key for the LLM service
                - model_name: Model to use
                - temperature: Temperature for generation

        Returns:
            A dictionary with:
                - answer: The generated answer
                - error: An error message if the operation failed
        """
        print(
            f"WARNING: Using legacy build method for {self.name}. Please update to use execute method."
        )
        print(f"Executing {self.name}...")

        # Check if openai is installed
        if not self._check_openai_installed():
            return {
                "error": "The openai package is required but not installed. Please install it with 'pip install openai'."
            }

        # Get inputs - prioritize handle inputs over direct inputs
        model_provider = kwargs.get("model_provider", "OpenAI")
        base_url = kwargs.get("base_url", "")
        api_key = kwargs.get("api_key")
        model_name = kwargs.get("model_name", "gpt-3.5-turbo")
        temperature = kwargs.get("temperature", 0.5)

        question_handle = kwargs.get("question_handle")
        question_direct = kwargs.get("question")
        context_handle = kwargs.get("context_handle")
        context_direct = kwargs.get("context")

        # Process inputs - prioritize handle inputs over direct inputs
        question = question_handle if question_handle is not None else question_direct
        context = context_handle if context_handle is not None else context_direct

        # Validate inputs
        if not question:
            return {
                "error": "Question is missing. Please connect a question or provide it directly."
            }
        if not context:
            return {
                "error": "Context document is missing. Please connect a context document or provide it directly."
            }
        if not api_key:
            return {"error": "API key is required."}

        try:
            # Import openai
            import openai

            # Set API key and base URL if provided
            openai.api_key = api_key

            # Set base URL if provided and using custom provider
            if model_provider == "Custom" and base_url:
                openai.api_base = base_url
            elif model_provider == "Azure OpenAI":
                # For Azure, we need to set the API type and version
                openai.api_type = "azure"
                openai.api_version = "2023-05-15"
                if base_url:
                    openai.api_base = base_url

            # Create system prompt for question answering
            system_prompt = "You are a question answering assistant. Answer the question based ONLY on the provided context. If the context doesn't contain the information needed to answer the question, say 'I don't have enough information to answer this question.'"

            # Create user prompt combining the question and context
            user_prompt = f"Context: {context}\n\nQuestion: {question}"

            # Make API call
            response = openai.ChatCompletion.create(
                model=model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                temperature=temperature,
            )

            # Extract content from response
            answer = response.choices[0].message.content.strip()

            print(f"  Question answered successfully.")
            return {"answer": answer}

        except Exception as e:
            error_msg = f"Error answering question: {str(e)}"
            print(f"  {error_msg}")
            return {"error": error_msg}
