from typing import Dict, Any, List, ClassVar
import importlib

from app.components.ai.base_agent_component import (
    BaseAgentComponent,
)
from app.models.workflow_builder.components import InputBase, HandleInput, StringInput
from app.models.workflow_builder.components import Output


class InformationExtractor(BaseAgentComponent):
    """
    Extracts specific pieces of information from a given text based on a query or instruction.

    This component uses an LLM to extract structured information (like names, dates, structured data)
    from a given text based on a query or instruction.
    """

    name: ClassVar[str] = "InformationExtractor"
    display_name: ClassVar[str] = "Information Extractor"
    description: ClassVar[str] = "Extracts specific information from text based on a query."

    icon: ClassVar[str] = "FileSearch"

    # Define component-specific inputs
    component_inputs: ClassVar[List[InputBase]] = [
        # Source text - connection handle
        HandleInput(
            name="text_handle",
            display_name="Source Text",
            required=True,
            is_handle=True,
            input_types=["string", "Any"],
            info="Connect the text from which to extract information.",
        ),
        # Source text - direct input in inspector
        StringInput(
            name="text",
            display_name="Source Text (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="The text from which to extract information. Used if no connection is provided.",
        ),
        # Query - connection handle
        HandleInput(
            name="query_handle",
            display_name="Extraction Query",
            is_handle=True,
            input_types=["string"],
            info="Connect the query specifying what information to extract.",
        ),
        # Query - direct input in inspector
        StringInput(
            name="query",
            display_name="Extraction Query (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="Specifies what information to extract (e.g., 'Extract email addresses', 'Find the customer name'). Used if no connection is provided.",
        ),
    ]

    # Combine with model client configuration inputs from BaseAgentComponent
    inputs: ClassVar[List[InputBase]] = [
        input_def
        for input_def in BaseAgentComponent.inputs
        if input_def.name in ["model_provider", "base_url", "api_key", "model_name"]
    ] + component_inputs

    outputs: ClassVar[List[Output]] = [
        Output(name="extracted_info", display_name="Extracted Information", output_type="string"),
        Output(name="error", display_name="Error", output_type="str"),
    ]

    def _check_openai_installed(self) -> bool:
        """
        Checks if the openai package is installed.

        Returns:
            True if openai is installed, False otherwise.
        """
        try:
            importlib.import_module("openai")
            return True
        except ImportError:
            return False

    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the InformationExtractor.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Extracts information from text based on a query.

        Args:
            **kwargs: Contains the input values:
                - text: The source text from which to extract information
                - query: The query specifying what information to extract
                - api_key: API key for the LLM service
                - model_name: Model to use
                - temperature: Temperature for generation

        Returns:
            A dictionary with:
                - extracted_info: The extracted information
                - error: An error message if the operation failed
        """
        print(
            f"WARNING: Using legacy build method for {self.name}. Please update to use execute method."
        )
        print(f"Executing {self.name}...")

        # Check if openai is installed
        if not self._check_openai_installed():
            return {
                "error": "The openai package is required but not installed. Please install it with 'pip install openai'."
            }

        # Get inputs - prioritize handle inputs over direct inputs
        model_provider = kwargs.get("model_provider", "OpenAI")
        base_url = kwargs.get("base_url", "")
        api_key = kwargs.get("api_key")
        model_name = kwargs.get("model_name", "gpt-3.5-turbo")
        temperature = kwargs.get(
            "temperature", 0.3
        )  # Lower temperature for more deterministic extraction

        text_handle = kwargs.get("text_handle")
        text_direct = kwargs.get("text")
        query_handle = kwargs.get("query_handle")
        query_direct = kwargs.get("query")

        # Process inputs - prioritize handle inputs over direct inputs
        text = text_handle if text_handle is not None else text_direct
        query = query_handle if query_handle is not None else query_direct

        # Validate inputs
        if not text:
            return {"error": "Source text is missing. Please connect text or provide it directly."}
        if not query:
            return {
                "error": "Extraction query is missing. Please connect a query or provide it directly."
            }
        if not api_key:
            return {"error": "API key is required."}

        try:
            # Import openai
            import openai

            # Set API key and base URL if provided
            openai.api_key = api_key

            # Set base URL if provided and using custom provider
            if model_provider == "Custom" and base_url:
                openai.api_base = base_url
            elif model_provider == "Azure OpenAI":
                # For Azure, we need to set the API type and version
                openai.api_type = "azure"
                openai.api_version = "2023-05-15"
                if base_url:
                    openai.api_base = base_url

            # Create system prompt for extraction
            system_prompt = "You are an information extraction assistant. Extract the requested information from the provided text. Only return the extracted information, nothing else."

            # Create user prompt combining the query and text
            user_prompt = f"Query: {query}\n\nText: {text}"

            # Make API call
            response = openai.ChatCompletion.create(
                model=model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                temperature=temperature,
            )

            # Extract content from response
            extracted_info = response.choices[0].message.content.strip()

            print(f"  Information extracted successfully.")
            return {"extracted_info": extracted_info}

        except Exception as e:
            error_msg = f"Error extracting information: {str(e)}"
            print(f"  {error_msg}")
            return {"error": error_msg}
