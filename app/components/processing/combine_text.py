from typing import Dict, Any, List, ClassVar
import asyncio
import logging
import json
import time

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    StringInput,
    HandleInput,
    IntInput,
    InputVisibilityRule,
)
from app.models.workflow_builder.components import Output
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeResult

from app.utils.workflow_builder.input_helpers import create_dual_purpose_input

logger = logging.getLogger(__name__)


class CombineTextComponent(BaseNode):
    """
    Joins text inputs with a separator, supporting a variable number of inputs.

    This component takes a main input and a configurable number of additional text inputs,
    each with its own connection handle, and joins them with the specified separator.
    """

    name: ClassVar[str] = "CombineTextComponent"
    display_name: ClassVar[str] = "Combine Text"
    description: ClassVar[str] = (
        "Joins text inputs with a separator, supporting a variable number of inputs."
    )
    category: ClassVar[str] = "Processing"
    icon: ClassVar[str] = "Link"

    # Define a maximum number of additional inputs to support
    MAX_ADDITIONAL_INPUTS = 10
    DEFAULT_ADDITIONAL_INPUTS = 2

    # Generate the inputs dynamically
    inputs: ClassVar[List[InputBase]] = [
        # Main input - single input that can be both connected and directly edited
        create_dual_purpose_input(
            name="main_input",
            display_name="Main Input",
            input_type="string",
            required=False,
            info="The main text or list to combine. Can be connected from another node or entered directly.",
            input_types=["string", "list", "Any"],
        ),
        # Number of additional inputs to show
        IntInput(
            name="num_additional_inputs",
            display_name="Number of Additional Inputs",
            value=DEFAULT_ADDITIONAL_INPUTS,  # Default to 2 additional inputs
            info=f"Set the number of additional text inputs to show (1-{MAX_ADDITIONAL_INPUTS}).",
        ),
        StringInput(
            name="separator",
            display_name="Separator",
            value="\\n",
            info="The character or string to join the text with.",
        ),
    ]

    # Add additional input fields dynamically
    for i in range(1, MAX_ADDITIONAL_INPUTS + 1):
        # Create visibility rules - show if num_additional_inputs is >= i
        # Since we don't have a "greater than or equal" operator, we create a rule for each possible value
        visibility_rules = [
            InputVisibilityRule(field_name="num_additional_inputs", field_value=j)
            for j in range(i, MAX_ADDITIONAL_INPUTS + 1)
        ]

        # Add single input that can be both connected and directly edited
        inputs.append(
            create_dual_purpose_input(
                name=f"input_{i}",
                display_name=f"Input {i}",
                input_type="string",
                required=False,
                info=f"Text for input {i}. Can be connected from another node or entered directly.",
                input_types=["string", "Any"],
                visibility_rules=visibility_rules,
            )
        )

    outputs: ClassVar[List[Output]] = [
        Output(name="output_text", display_name="Combined Text", output_type="string"),
        Output(name="error", display_name="Error", output_type="str"),
    ]

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """
        Execute the CombineTextComponent.

        This method combines text from a main input and a variable number of additional inputs
        using the specified separator.

        Args:
            context: The workflow execution context containing input values.

        Returns:
            A NodeResult with the execution results.
        """
        # Start timing for performance measurement
        start_time = time.time()

        # Log execution start
        context.log(f"Executing {self.name}...")

        try:
            # Get inputs from context
            main_input = self.get_input_value("main_input", context, "")
            num_additional_inputs = min(
                int(self.get_input_value("num_additional_inputs", context, self.DEFAULT_ADDITIONAL_INPUTS)),
                self.MAX_ADDITIONAL_INPUTS
            )
            separator = self.get_input_value("separator", context, "\\n")

            # Log input values for debugging
            logger.debug(f"Main input type: {type(main_input).__name__}")
            logger.debug(f"Number of additional inputs: {num_additional_inputs}")
            logger.debug(f"Separator: '{separator}'")

            # Prepare the tool parameters for the node-executor-service
            tool_parameters = {
                "main_input": main_input,
                "num_additional_inputs": num_additional_inputs,
                "separator": separator
            }

            # Add additional inputs to the parameters
            for i in range(1, self.MAX_ADDITIONAL_INPUTS + 1):
                input_name = f"input_{i}"
                if i <= num_additional_inputs:
                    input_value = self.get_input_value(input_name, context, "")
                    tool_parameters[input_name] = input_value
                else:
                    # Include all possible inputs with null values for ones not used
                    tool_parameters[input_name] = None

            # Log the tool parameters
            logger.debug(f"Tool parameters: {tool_parameters}")

            # Call the node-executor-service to execute the component
            # This is handled by the orchestration engine, which will:
            # 1. Send a request to the node-executor-service with the tool_parameters
            # 2. The node-executor-service will process the request and return a result
            # 3. The orchestration engine will pass the result back to us

            # Try to parse main_input as JSON if it looks like a list or dict and is a string
            if (
                main_input
                and isinstance(main_input, str)
                and (
                    (main_input.startswith("[") and main_input.endswith("]"))
                    or (main_input.startswith("{") and main_input.endswith("}"))
                )
            ):
                try:
                    main_input = json.loads(main_input)
                    logger.debug(f"Parsed main input as JSON: {type(main_input).__name__}")
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse main input as JSON: {e}")
                    # Keep as string if parsing fails

            # Process literal escape sequences in separator
            processed_separator = separator.replace("\\n", "\n").replace("\\t", "\t").replace("\\r", "\r")

            # Validate input
            if not main_input and main_input != 0:  # Allow 0 as a valid input
                error_msg = "Main input is missing. Please provide a main input either via connection or direct input."
                context.log(error_msg)
                return NodeResult.error(error_message=error_msg)

            # Prepare the list of strings to join
            strings_to_join = []

            # Process the main input
            if isinstance(main_input, list):
                # Convert all list items to strings
                for item in main_input:
                    if item is not None:  # Skip None values
                        strings_to_join.append(str(item))
                        logger.debug(f"Added item from main_input list: {str(item)[:50]}...")
                    else:
                        logger.debug("Skipping None item in main_input list")
            else:
                # Convert single input to string
                strings_to_join.append(str(main_input))
                logger.debug(f"Added main_input as string: {str(main_input)[:50]}...")

            # Process additional inputs
            for i in range(1, num_additional_inputs + 1):
                # Get input value
                input_name = f"input_{i}"
                input_value = self.get_input_value(input_name, context, "")

                # Add to strings if not empty and not None
                if input_value is not None and input_value != "":
                    strings_to_join.append(str(input_value))
                    logger.debug(f"Added {input_name}: {str(input_value)[:50]}...")
                else:
                    logger.debug(f"Skipping empty or None {input_name}")

            # Join the strings with the separator
            result = processed_separator.join(strings_to_join)

            # Log success
            execution_time = time.time() - start_time
            context.log(f"Text combined successfully. Length: {len(result)}. Time: {execution_time:.2f}s")
            logger.info(f"Text combined successfully. Length: {len(result)}. Time: {execution_time:.2f}s")

            # Return success result
            return NodeResult.success(
                outputs={"output_text": result},
                execution_time=execution_time
            )

        except Exception as e:
            # Log error
            error_msg = f"Error combining text: {str(e)}"
            context.log(error_msg)
            logger.error(f"{error_msg}", exc_info=True)

            # Return error result
            return NodeResult.error(
                error_message=error_msg,
                execution_time=time.time() - start_time
            )

    def get_input_value(self, input_name: str, context: WorkflowContext, default: Any = None) -> Any:
        """
        Get the value of an input from the context.

        This method handles both direct inputs and handle inputs, prioritizing handle inputs.

        Args:
            input_name: The name of the input.
            context: The workflow execution context.
            default: The default value to return if the input is not found.

        Returns:
            The input value, or the default value if not found.
        """
        # Get all inputs for the current node
        node_inputs = context.node_outputs.get(context.current_node_id, {})

        # Check for handle input first (prioritize connected values)
        handle_value = node_inputs.get(f"{input_name}")
        if handle_value is not None:
            return handle_value

        # If no handle input, check for direct input
        direct_value = node_inputs.get(input_name)
        if direct_value is not None:
            return direct_value

        # If neither is found, return the default value
        return default

    # Legacy method for backward compatibility
    def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the CombineTextComponent.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Args:
            **kwargs: Contains the input values.

        Returns:
            A dictionary with the component's outputs.
        """
        logger.warning(f"Using legacy build method for {self.name}. Please update to use execute method.")

        # Get main inputs
        main_input = kwargs.get("main_input", "")
        num_additional_inputs = min(
            int(kwargs.get("num_additional_inputs", 1)), self.MAX_ADDITIONAL_INPUTS
        )
        separator = kwargs.get("separator", "\\n")

        # Try to parse main_input as JSON if it looks like a list or dict and is a string
        if (
            main_input
            and isinstance(main_input, str)
            and (
                (main_input.startswith("[") and main_input.endswith("]"))
                or (main_input.startswith("{") and main_input.endswith("}"))
            )
        ):
            try:
                main_input = json.loads(main_input)
                logger.debug(f"Parsed main input as JSON: {type(main_input).__name__}")
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse main input as JSON: {e}")
                # Keep as string if parsing fails

        # Process literal escape sequences in separator
        separator = separator.replace("\\n", "\n").replace("\\t", "\t").replace("\\r", "\r")

        # Validate input
        if not main_input and main_input != 0:  # Allow 0 as a valid input
            return {
                "error": "Main input is missing. Please provide a main input either via connection or direct input."
            }

        try:
            # Prepare the list of strings to join
            strings_to_join = []

            # Process the main input
            if isinstance(main_input, list):
                # Convert all list items to strings
                for item in main_input:
                    if item is not None:  # Skip None values
                        strings_to_join.append(str(item))
            else:
                # Convert single input to string
                strings_to_join.append(str(main_input))

            # Process additional inputs
            for i in range(1, num_additional_inputs + 1):
                # Get input value
                input_value = kwargs.get(f"input_{i}", "")

                # Add to strings if not empty
                if input_value:
                    strings_to_join.append(str(input_value))

            # Join the strings with the separator
            result = separator.join(strings_to_join)
            logger.info(f"Text combined successfully. Length: {len(result)}")
            return {"output_text": result}

        except Exception as e:
            error_msg = f"Error combining text: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
