{"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 100, "y": 100}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "Input/Output", "icon": "Play", "beta": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any"}], "is_valid": true, "path": "components.io.start_node"}, "config": {"collected_parameters": {"MCP_Candidate_Interview_candidate_suitability_pre-1747901636059_resume_s3_link": {"node_id": "MCP_Candidate_Interview_candidate_suitability_pre-1747901636059", "node_name": "Candidate Interview - candidate_suitability_pre", "input_name": "resume_s3_link", "connected_to_start": true, "required": true, "input_type": "string", "options": null}, "MCP_Candidate_Interview_candidate_suitability_pre-1747901636059_job_description_s3_link": {"node_id": "MCP_Candidate_Interview_candidate_suitability_pre-1747901636059", "node_name": "Candidate Interview - candidate_suitability_pre", "input_name": "job_description_s3_link", "connected_to_start": true, "required": true, "input_type": "string", "options": null}, "ApiRequestNode-1748247037864_body": {"node_id": "ApiRequestNode-1748247037864", "node_name": "API Request", "input_name": "body", "connected_to_start": true, "required": true, "input_type": "dict", "options": null}}}}, "width": 208, "height": 194, "selected": false, "dragging": false}, {"id": "MCP_Candidate_Interview_candidate_suitability_pre-1747901636059", "type": "WorkflowNode", "position": {"x": 500, "y": -100}, "data": {"label": "Candidate Interview - candidate_suitability_pre", "type": "mcp", "originalType": "MCP_Candidate_Interview_candidate_suitability_pre", "definition": {"name": "MCP_Candidate_Interview_candidate_suitability_pre", "display_name": "Candidate Interview - candidate_suitability_pre", "description": "Analyze candidate suitability based on job description and resume", "category": "MCP", "icon": "Cloud", "beta": true, "inputs": [{"name": "resume_s3_link", "display_name": "resume s3 link", "info": "Candidate's resume", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "job_description_s3_link", "display_name": "job description s3 link", "info": "Job description", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "suitability_analysis", "display_name": "suitability analysis", "output_type": "string"}, {"name": "resume_details", "display_name": "resume details", "output_type": "string"}, {"name": "jd_details", "display_name": "jd details", "output_type": "string"}], "is_valid": true, "path": "mcp.mcp_candidate_interview_candidate_suitability_pre", "type": "MCP", "mcp_info": {"server_id": "47d735d3-cd2b-4b40-9921-e0946c94dc31", "server_path": "https://mcp.interview.rapidinnovation.dev/sse", "tool_name": "candidate_suitability_pre", "input_schema": {"type": "object", "properties": {"resume_s3_link": {"type": "string", "description": "Candidate's resume"}, "job_description_s3_link": {"type": "string", "description": "Job description"}}, "required": ["job_description_s3_link", "resume_s3_link"]}, "output_schema": {"type": "object", "properties": {"suitability_analysis": {"type": "string", "description": "Analysis of candidate's suitability for the job"}, "resume_details": {"type": "string", "description": "Candidate's resume"}, "jd_details": {"type": "string", "description": "Interview job description"}}, "required": []}}}, "config": {}}, "width": 208, "height": 289, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_Candidate_Interview_generate_interview_agenda-1747901643530", "type": "WorkflowNode", "position": {"x": 880, "y": 140}, "data": {"label": "Candidate Interview - generate_interview_agenda", "type": "mcp", "originalType": "MCP_Candidate_Interview_generate_interview_agenda", "definition": {"name": "MCP_Candidate_Interview_generate_interview_agenda", "display_name": "Candidate Interview - generate_interview_agenda", "description": "Generate interview agenda based on job description and resume", "category": "MCP", "icon": "Cloud", "beta": true, "inputs": [{"name": "resume_details", "display_name": "resume details", "info": "Candidate's resume", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "jd_details", "display_name": "jd details", "info": "Job description", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "prompt", "display_name": "prompt", "info": "Optional custom prompt to guide the agenda generation", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "interview_agenda", "display_name": "interview agenda", "output_type": "string"}, {"name": "resume_details", "display_name": "resume details", "output_type": "string"}, {"name": "jd_details", "display_name": "jd details", "output_type": "string"}], "is_valid": true, "path": "mcp.mcp_candidate_interview_generate_interview_agenda", "type": "MCP", "mcp_info": {"server_id": "47d735d3-cd2b-4b40-9921-e0946c94dc31", "server_path": "https://mcp.interview.rapidinnovation.dev/sse", "tool_name": "generate_interview_agenda", "input_schema": {"type": "object", "properties": {"resume_details": {"type": "string", "description": "Candidate's resume"}, "jd_details": {"type": "string", "description": "Job description"}, "prompt": {"type": "string", "description": "Optional custom prompt to guide the agenda generation"}}, "required": ["jd_details", "resume_details"]}, "output_schema": {"type": "object", "properties": {"interview_agenda": {"type": "string", "description": "Generated interview agenda"}, "resume_details": {"type": "string", "description": "Candidate's resume"}, "jd_details": {"type": "string", "description": "Interview job description"}}, "required": []}}}, "config": {}}, "width": 208, "height": 313, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_Candidate_Interview_generate_questions-1747901658176", "type": "WorkflowNode", "position": {"x": 1240, "y": -200}, "data": {"label": "Candidate Interview - generate_questions", "type": "mcp", "originalType": "MCP_Candidate_Interview_generate_questions", "definition": {"name": "MCP_Candidate_Interview_generate_questions", "display_name": "Candidate Interview - generate_questions", "description": "Generate interview questions based on job description and resume for each agenda", "category": "MCP", "icon": "Cloud", "beta": true, "inputs": [{"name": "resume_details", "display_name": "resume details", "info": "Candidate's resume", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "jd_details", "display_name": "jd details", "info": "Job description", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "agenda", "display_name": "agenda", "info": "Agenda in string format to generate questions from", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "question_count", "display_name": "question count", "info": "How many questions to generate for each agenda (max 5)", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "interview_questions", "display_name": "interview questions", "output_type": "string"}, {"name": "resume_details", "display_name": "resume details", "output_type": "string"}, {"name": "jd_details", "display_name": "jd details", "output_type": "string"}], "is_valid": true, "path": "mcp.mcp_candidate_interview_generate_questions", "type": "MCP", "mcp_info": {"server_id": "47d735d3-cd2b-4b40-9921-e0946c94dc31", "server_path": "https://mcp.interview.rapidinnovation.dev/sse", "tool_name": "generate_questions", "input_schema": {"type": "object", "properties": {"resume_details": {"type": "string", "description": "Candidate's resume"}, "jd_details": {"type": "string", "description": "Job description"}, "agenda": {"type": "string", "description": "Agenda in string format to generate questions from"}, "question_count": {"type": "string", "description": "How many questions to generate for each agenda (max 5)"}}, "required": ["agenda", "jd_details", "resume_details"]}, "output_schema": {"type": "object", "properties": {"interview_questions": {"type": "string", "description": "Generated interview questions"}, "resume_details": {"type": "string", "description": "Candidate's resume"}, "jd_details": {"type": "string", "description": "Interview job description"}}, "required": []}}}, "config": {}}, "width": 208, "height": 337, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "SelectDataComponent-1748238994773", "type": "WorkflowNode", "position": {"x": 1160, "y": 560}, "data": {"label": "Select Data", "type": "component", "originalType": "SelectDataComponent", "definition": {"name": "SelectDataComponent", "display_name": "Select Data", "description": "Extracts elements from lists or dictionaries.", "category": "Processing", "icon": "Filter", "beta": false, "requires_approval": false, "inputs": [{"name": "input_data", "display_name": "Input Data", "info": "The data to select from. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["list", "dict", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "data_type", "display_name": "Data Type", "info": "The type of data structure to select from.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "Auto-Detect", "options": ["Auto-Detect", "List", "Dictionary"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "search_mode", "display_name": "Search Mode", "info": "Exact Path: Use precise path notation (e.g., 'user.name'). Smart Search: Find field by name anywhere in the structure (e.g., 'email' finds first occurrence).", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "Exact Path", "options": ["Exact Path", "Smart Search"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "field_matching_mode", "display_name": "Field Matching Mode", "info": "Auto-detect: Try key-based first, fallback to property-based. Key-based Only: Traditional object keys. Property-based Only: Search property_name fields. Use @ notation for property-based paths (e.g., 'result.@script').", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "Auto-detect", "options": ["Auto-detect", "Key-based Only", "Property-based Only"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "selector", "display_name": "Selector", "info": "For lists: index or slice (e.g., '0', '1:5'). For dicts: key name or path (e.g., 'user.name') in Exact Path mode, or field name (e.g., 'email') in Smart Search mode. Use @ notation for property-based paths (e.g., 'result.@script' finds object where property_name='script'). Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "output_data", "display_name": "Selected Data", "output_type": "Any", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.processing.selectdatacomponent", "interface_issues": []}, "config": {"data_type": "Auto-Detect", "search_mode": "Smart Search", "field_matching_mode": "Key-based Only", "selector": "interview_id"}}, "width": 208, "height": 265, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "CombineTextComponent-1748239039848", "type": "WorkflowNode", "position": {"x": 1680, "y": 460}, "data": {"label": "Combine Text", "type": "component", "originalType": "CombineTextComponent", "definition": {"name": "CombineTextComponent", "display_name": "Combine Text", "description": "Joins text inputs with a separator, supporting a variable number of inputs.", "category": "Processing", "icon": "Link", "beta": false, "requires_approval": false, "inputs": [{"name": "main_input", "display_name": "Main Input", "info": "The main text or list to combine. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "num_additional_inputs", "display_name": "Number of Additional Inputs", "info": "Set the number of additional text inputs to show (1-10).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 2, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "separator", "display_name": "Separator", "info": "The character or string to join the text with.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "\\n", "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "input_1", "display_name": "Input 1", "info": "Text for input 1. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_2", "display_name": "Input 2", "info": "Text for input 2. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_3", "display_name": "Input 3", "info": "Text for input 3. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_4", "display_name": "Input 4", "info": "Text for input 4. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_5", "display_name": "Input 5", "info": "Text for input 5. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_6", "display_name": "Input 6", "info": "Text for input 6. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_7", "display_name": "Input 7", "info": "Text for input 7. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_8", "display_name": "Input 8", "info": "Text for input 8. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_9", "display_name": "Input 9", "info": "Text for input 9. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_10", "display_name": "Input 10", "info": "Text for input 10. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}], "outputs": [{"name": "output_text", "display_name": "Combined Text", "output_type": "string", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.processing.combinetextcomponent", "interface_issues": []}, "config": {"num_additional_inputs": "1", "main_input": "https://interview.rapidinnovation.dev/api/v1/interviews", "separator": "/"}}, "width": 208, "height": 265, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MergeDataComponent-1748239108834", "type": "WorkflowNode", "position": {"x": 1800, "y": 40}, "data": {"label": "Merge Data", "type": "component", "originalType": "MergeDataComponent", "definition": {"name": "MergeDataComponent", "display_name": "Merge Data", "description": "Combines multiple dictionaries or lists.", "category": "Processing", "icon": "Combine", "beta": false, "requires_approval": false, "inputs": [{"name": "main_input", "display_name": "Main Input", "info": "The main data structure to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "num_additional_inputs", "display_name": "Number of Additional Inputs", "info": "Set the number of additional inputs to show (1-10).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 2, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "merge_strategy", "display_name": "Merge Strategy (Dicts)", "info": "How to handle conflicts when merging dictionaries.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "Overwrite", "options": ["Overwrite", "Deep Merge", "Error on Conflict"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "input_1", "display_name": "Input 1", "info": "Data structure 1 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_2", "display_name": "Input 2", "info": "Data structure 2 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_3", "display_name": "Input 3", "info": "Data structure 3 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_4", "display_name": "Input 4", "info": "Data structure 4 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_5", "display_name": "Input 5", "info": "Data structure 5 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_6", "display_name": "Input 6", "info": "Data structure 6 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_7", "display_name": "Input 7", "info": "Data structure 7 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_8", "display_name": "Input 8", "info": "Data structure 8 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_9", "display_name": "Input 9", "info": "Data structure 9 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_10", "display_name": "Input 10", "info": "Data structure 10 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}], "outputs": [{"name": "output_data", "display_name": "Merged Data", "output_type": "Any", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.processing.mergedatacomponent", "interface_issues": []}, "config": {"merge_strategy": "Deep Merge", "num_additional_inputs": "1"}}, "width": 208, "height": 265, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "ApiRequestNode-1748247037864", "type": "WorkflowNode", "position": {"x": 800, "y": 540}, "data": {"label": "API Request", "type": "component", "originalType": "ApiRequestNode", "definition": {"name": "ApiRequestNode", "display_name": "API Request", "description": "Makes a single HTTP request to the specified URL.", "category": "Data Interaction", "icon": "Globe", "beta": false, "requires_approval": false, "inputs": [{"name": "url", "display_name": "URL", "info": "The URL to make the request to.", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "method", "display_name": "HTTP Method", "info": "The HTTP method to use for the request.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": true, "advanced": false, "value": "GET", "options": ["GET", "POST", "PUT", "PATCH", "DELETE"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "query_params", "display_name": "Query Parameters", "info": "Key-value pairs to append to the URL query string (optional).", "input_type": "dict", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": true, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "headers", "display_name": "Request Headers", "info": "Key-value pairs for request headers (optional).", "input_type": "dict", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "body", "display_name": "Request Body (JSON)", "info": "Key-value dictionary for the request body (used for POST, PUT, PATCH). Will be JSON serialized.", "input_type": "dict", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": true, "value": {}, "options": null, "visibility_rules": [{"field_name": "method", "field_value": "POST", "operator": "equals"}, {"field_name": "method", "field_value": "PUT", "operator": "equals"}, {"field_name": "method", "field_value": "PATCH", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "timeout", "display_name": "Timeout (seconds)", "info": "Maximum time to wait for a response.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": 10, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "follow_redirects", "display_name": "Follow Redirects", "info": "Automatically follow HTTP redirects (e.g., 301, 302).", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "save_to_file", "display_name": "Save Response to File", "info": "Save response body to a temporary file instead of returning content directly. 'Result' output will be the file path.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": false, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "output_format", "display_name": "Output Format", "info": "'auto': Try JSON, fallback to text. 'json': Force JSON parsing (error if invalid). 'text': Return as text. 'bytes': Return raw bytes. 'file_path': Always save to file (requires 'Save Response to File'). 'metadata_dict': Return dict with status, headers, body/path, error.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "auto", "options": ["auto", "json", "text", "bytes", "file_path", "metadata_dict"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "raise_on_error", "display_name": "Raise Exception on HTTP Error", "info": "Stop workflow execution if an HTTP error status (4xx, 5xx) is received.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": false, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "Any", "method": null}, {"name": "status_code", "display_name": "Status Code", "output_type": "int", "method": null}, {"name": "response_headers", "display_name": "Response Headers", "output_type": "dict", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.data interaction.apirequestnode", "interface_issues": []}, "config": {"method": "POST", "url": "https://interview.rapidinnovation.dev/api/v1/interviews/", "headers": {"Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************.OimpZNQlzEHUuXpYLV2mpCJEyG7bzs24tVojOl6y8lo"}}}, "width": 208, "height": 337, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "ApiRequestNode-1748247226155", "type": "WorkflowNode", "position": {"x": 2200, "y": 240}, "data": {"label": "API Request", "type": "component", "originalType": "ApiRequestNode", "definition": {"name": "ApiRequestNode", "display_name": "API Request", "description": "Makes a single HTTP request to the specified URL.", "category": "Data Interaction", "icon": "Globe", "beta": false, "requires_approval": false, "inputs": [{"name": "url", "display_name": "URL", "info": "The URL to make the request to.", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "method", "display_name": "HTTP Method", "info": "The HTTP method to use for the request.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": true, "advanced": false, "value": "GET", "options": ["GET", "POST", "PUT", "PATCH", "DELETE"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "query_params", "display_name": "Query Parameters", "info": "Key-value pairs to append to the URL query string (optional).", "input_type": "dict", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": true, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "headers", "display_name": "Request Headers", "info": "Key-value pairs for request headers (optional).", "input_type": "dict", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "body", "display_name": "Request Body (JSON)", "info": "Key-value dictionary for the request body (used for POST, PUT, PATCH). Will be JSON serialized.", "input_type": "dict", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": true, "value": {}, "options": null, "visibility_rules": [{"field_name": "method", "field_value": "POST", "operator": "equals"}, {"field_name": "method", "field_value": "PUT", "operator": "equals"}, {"field_name": "method", "field_value": "PATCH", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "timeout", "display_name": "Timeout (seconds)", "info": "Maximum time to wait for a response.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": 10, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "follow_redirects", "display_name": "Follow Redirects", "info": "Automatically follow HTTP redirects (e.g., 301, 302).", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "save_to_file", "display_name": "Save Response to File", "info": "Save response body to a temporary file instead of returning content directly. 'Result' output will be the file path.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": false, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "output_format", "display_name": "Output Format", "info": "'auto': Try JSON, fallback to text. 'json': Force JSON parsing (error if invalid). 'text': Return as text. 'bytes': Return raw bytes. 'file_path': Always save to file (requires 'Save Response to File'). 'metadata_dict': Return dict with status, headers, body/path, error.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "auto", "options": ["auto", "json", "text", "bytes", "file_path", "metadata_dict"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "raise_on_error", "display_name": "Raise Exception on HTTP Error", "info": "Stop workflow execution if an HTTP error status (4xx, 5xx) is received.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": false, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "Any", "method": null}, {"name": "status_code", "display_name": "Status Code", "output_type": "int", "method": null}, {"name": "response_headers", "display_name": "Response Headers", "output_type": "dict", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.data interaction.apirequestnode", "interface_issues": []}, "config": {"method": "PUT", "headers": {"Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************.OimpZNQlzEHUuXpYLV2mpCJEyG7bzs24tVojOl6y8lo"}}}, "width": 208, "height": 337, "selected": false, "dragging": false, "style": {"opacity": 1}}], "edges": [{"id": "reactflow__edge-start-nodeflow-MCP_Candidate_Interview_candidate_suitability_pre-1747901636059resume_s3_link", "source": "start-node", "sourceHandle": "flow", "target": "MCP_Candidate_Interview_candidate_suitability_pre-1747901636059", "targetHandle": "resume_s3_link", "animated": true}, {"id": "reactflow__edge-MCP_Candidate_Interview_candidate_suitability_pre-1747901636059resume_details-MCP_Candidate_Interview_generate_interview_agenda-1747901643530resume_details", "source": "MCP_Candidate_Interview_candidate_suitability_pre-1747901636059", "sourceHandle": "resume_details", "target": "MCP_Candidate_Interview_generate_interview_agenda-1747901643530", "targetHandle": "resume_details", "animated": true}, {"id": "reactflow__edge-MCP_Candidate_Interview_candidate_suitability_pre-1747901636059jd_details-MCP_Candidate_Interview_generate_interview_agenda-1747901643530jd_details", "source": "MCP_Candidate_Interview_candidate_suitability_pre-1747901636059", "sourceHandle": "jd_details", "target": "MCP_Candidate_Interview_generate_interview_agenda-1747901643530", "targetHandle": "jd_details", "animated": true}, {"id": "reactflow__edge-MCP_Candidate_Interview_generate_interview_agenda-1747901643530interview_agenda-MCP_Candidate_Interview_generate_questions-1747901658176agenda", "source": "MCP_Candidate_Interview_generate_interview_agenda-1747901643530", "sourceHandle": "interview_agenda", "target": "MCP_Candidate_Interview_generate_questions-1747901658176", "targetHandle": "agenda", "animated": true}, {"id": "reactflow__edge-MCP_Candidate_Interview_generate_interview_agenda-1747901643530resume_details-MCP_Candidate_Interview_generate_questions-1747901658176resume_details", "source": "MCP_Candidate_Interview_generate_interview_agenda-1747901643530", "sourceHandle": "resume_details", "target": "MCP_Candidate_Interview_generate_questions-1747901658176", "targetHandle": "resume_details", "animated": true}, {"id": "reactflow__edge-MCP_Candidate_Interview_generate_interview_agenda-1747901643530jd_details-MCP_Candidate_Interview_generate_questions-1747901658176jd_details", "source": "MCP_Candidate_Interview_generate_interview_agenda-1747901643530", "sourceHandle": "jd_details", "target": "MCP_Candidate_Interview_generate_questions-1747901658176", "targetHandle": "jd_details", "animated": true}, {"id": "reactflow__edge-SelectDataComponent-1748238994773output_data-CombineTextComponent-1748239039848input_1", "source": "SelectDataComponent-1748238994773", "sourceHandle": "output_data", "target": "CombineTextComponent-1748239039848", "targetHandle": "input_1", "animated": true}, {"id": "reactflow__edge-MC<PERSON>_Candidate_Interview_generate_questions-1747901658176interview_questions-MergeDataComponent-1748239108834main_input", "source": "MCP_Candidate_Interview_generate_questions-1747901658176", "sourceHandle": "interview_questions", "target": "MergeDataComponent-1748239108834", "targetHandle": "main_input", "animated": true}, {"id": "reactflow__edge-ApiRequestNode-1748247037864result-SelectDataComponent-1748238994773input_data", "source": "ApiRequestNode-1748247037864", "sourceHandle": "result", "target": "SelectDataComponent-1748238994773", "targetHandle": "input_data", "animated": true}, {"id": "reactflow__edge-start-nodeflow-ApiRequestNode-1748247037864body", "source": "start-node", "sourceHandle": "flow", "target": "ApiRequestNode-1748247037864", "targetHandle": "body", "animated": true}, {"id": "reactflow__edge-MergeDataComponent-1748239108834output_data-ApiRequestNode-1748247226155body", "source": "MergeDataComponent-1748239108834", "sourceHandle": "output_data", "target": "ApiRequestNode-1748247226155", "targetHandle": "body", "animated": true}, {"id": "reactflow__edge-CombineTextComponent-1748239039848output_text-ApiRequestNode-1748247226155url", "source": "CombineTextComponent-1748239039848", "sourceHandle": "output_text", "target": "ApiRequestNode-1748247226155", "targetHandle": "url", "animated": true}, {"id": "reactflow__edge-MCP_Candidate_Interview_generate_interview_agenda-1747901643530interview_agenda-MergeDataComponent-1748239108834input_1", "source": "MCP_Candidate_Interview_generate_interview_agenda-1747901643530", "sourceHandle": "interview_agenda", "target": "MergeDataComponent-1748239108834", "targetHandle": "input_1", "animated": true}]}